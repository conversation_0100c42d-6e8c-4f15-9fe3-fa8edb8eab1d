#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DBSCAN词汇相似度聚类效果
"""

import json
import numpy as np
from biology_dbscan_analysis import BiologyQADBSCANAnalyzer
import biology_dbscan_analysis
print(f"当前加载的DBSCAN分析文件路径: {biology_dbscan_analysis.__file__}")

def create_test_data():
    """创建测试数据"""
    test_data = [
        {"id": "1", "content": "人为什么会做梦？", "label": "生物"},
        {"id": "2", "content": "为什么人会做梦？", "label": "生物"},
        {"id": "3", "content": "人为什么做梦？", "label": "生物"},
        {"id": "4", "content": "大象的鼻子为什么长？", "label": "生物"},
        {"id": "5", "content": "为什么大象的鼻子长？", "label": "生物"},
        {"id": "6", "content": "大象鼻子为什么这么长？", "label": "生物"},
        {"id": "7", "content": "天空为什么是蓝色的？", "label": "生物"},
        {"id": "8", "content": "为什么天空是蓝色的？", "label": "生物"},
        {"id": "9", "content": "天空为什么蓝？", "label": "生物"},
        {"id": "16", "content": "天空是什么颜色的？", "label": "生物"},
        {"id": "10", "content": "萤火虫为什么会发光？", "label": "生物"},
        {"id": "11", "content": "为什么萤火虫会发光？", "label": "生物"},
        {"id": "12", "content": "萤火虫为什么发光？", "label": "生物"},
        {"id": "13", "content": "螃蟹煮熟了为什么会变色？", "label": "生物"},
        {"id": "14", "content": "为什么螃蟹煮熟了会变色？", "label": "生物"},
        {"id": "15", "content": "煮熟的螃蟹为什么变色？", "label": "生物"},
    ]
    
    return test_data


def test_similarity_calculation():
    """测试Jaccard相似度计算"""
    print("=== 测试DBSCAN Jaccard相似度计算 ===")

    # 创建DBSCAN分析器实例
    analyzer = BiologyQADBSCANAnalyzer("dummy_path")
    
    # 测试词汇集合
    set1 = {"人", "做梦"}
    set2 = {"做梦", "人"}
    set3 = {"大象", "鼻子", "长"}
    set4 = {"人", "睡觉"}
    
    print(f"集合1: {set1}")
    print(f"集合2: {set2}")
    print(f"Jaccard相似度: {analyzer.jaccard_similarity(set1, set2):.4f}")
    print()

    print(f"集合1: {set1}")
    print(f"集合3: {set3}")
    print(f"Jaccard相似度: {analyzer.jaccard_similarity(set1, set3):.4f}")
    print()

    print(f"集合1: {set1}")
    print(f"集合4: {set4}")
    print(f"Jaccard相似度: {analyzer.jaccard_similarity(set1, set4):.4f}")


def test_clustering():
    """测试DBSCAN聚类效果"""
    print("\n=== 测试DBSCAN聚类效果 ===")

    # 创建测试数据文件
    test_data = create_test_data()
    test_file = "/tmp/test_biology_qa_dbscan.json"

    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)

    # 创建DBSCAN分析器并运行分析
    analyzer = BiologyQADBSCANAnalyzer(test_file)
    
    # 加载数据
    analyzer.load_data()
    
    # 准备特征
    analyzer.prepare_features()
    
    # 显示预处理结果和详细分词信息
    print("\n预处理结果:")
    for i, (original, processed) in enumerate(zip(
        [item['content'] for item in analyzer.data],
        analyzer.processed_texts
    )):
        print(f"{i+1:2d}. 原文: {original}")

        # 显示详细的分词过程
        import jieba.posseg as pseg
        detailed_words = []
        for word, flag in pseg.cut(original):
            detailed_words.append(f"{word}({flag})")
        print(f"    详细分词: {' | '.join(detailed_words)}")

        print(f"    处理后: {processed}")
        print(f"    词汇集合: {analyzer.word_sets[i]}")
        print()
    
    # 显示相似度矩阵（处理稀疏矩阵的情况）
    print("相似度矩阵:")
    if hasattr(analyzer.similarity_matrix, 'toarray'):
        # 稀疏矩阵
        print("稀疏矩阵，显示密集版本:")
        print(np.round(analyzer.similarity_matrix.toarray(), 3))
    else:
        # 密集矩阵
        print(np.round(analyzer.similarity_matrix, 3))
    
    # 寻找最优参数并聚类
    analyzer.find_optimal_eps()
    analyzer.perform_clustering()

    # 显示聚类结果
    print("\n聚类结果:")
    for i, (original, cluster_id) in enumerate(zip(
        [item['content'] for item in analyzer.data],
        analyzer.cluster_labels
    )):
        if cluster_id == -1:
            print(f"{i+1:2d}. [噪声] {original}")
        else:
            print(f"{i+1:2d}. [聚类{cluster_id}] {original}")

    # 分析聚类特征
    print("\n开始聚类特征分析...")
    cluster_analysis = analyzer.analyze_clusters()

    # 保存结果
    print("\n保存聚类结果...")
    analyzer.save_results(cluster_analysis)

    print("\n=== DBSCAN聚类测试完成 ===")


if __name__ == "__main__":
    test_similarity_calculation()
    test_clustering()
