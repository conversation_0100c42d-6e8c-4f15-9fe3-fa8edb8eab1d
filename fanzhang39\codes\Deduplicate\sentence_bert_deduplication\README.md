# BGE-M3 多GPU文本去重系统

## 概述

基于BGE-M3模型的高性能文本去重系统，支持多GPU并行处理，采用GPU+CPU混合算法实现大规模文本数据的语义去重。

## 系统架构

```mermaid
graph TB
    %% 输入阶段
    A[JSON文件输入<br/>classified/*.json] --> B[文件大小分析<br/>统计问题数量]
    B --> C[贪心负载均衡算法<br/>按问题数分配到GPU]

    %% GPU分配
    C --> D1[GPU 1: cuda:1<br/>优先级最高]
    C --> D2[GPU 3: cuda:3<br/>优先级第二]
    C --> D3[GPU 0: cuda:0<br/>优先级第三]
    C --> D4[GPU 2: cuda:2<br/>优先级第四]

    %% 数据预处理
    D1 --> E1[读取JSON数据<br/>提取id+content+label]
    D2 --> E2[读取JSON数据<br/>提取id+content+label]
    D3 --> E3[读取JSON数据<br/>提取id+content+label]
    D4 --> E4[读取JSON数据<br/>提取id+content+label]

    %% BGE-M3模型编码
    E1 --> F1[BGE-M3模型加载<br/>BAAI/bge-m3<br/>max_length=512]
    E2 --> F2[BGE-M3模型加载<br/>BAAI/bge-m3<br/>max_length=512]
    E3 --> F3[BGE-M3模型加载<br/>BAAI/bge-m3<br/>max_length=512]
    E4 --> F4[BGE-M3模型加载<br/>BAAI/bge-m3<br/>max_length=512]

    F1 --> G1[文本编码<br/>batch_size=64<br/>输出1024维向量]
    F2 --> G2[文本编码<br/>batch_size=64<br/>输出1024维向量]
    F3 --> G3[文本编码<br/>batch_size=64<br/>输出1024维向量]
    F4 --> G4[文本编码<br/>batch_size=64<br/>输出1024维向量]

    %% 相似度计算详细流程
    G1 --> H1A[批处理循环<br/>i=0 to N, step=100<br/>分批处理避免内存溢出]
    G2 --> H2A[批处理循环<br/>i=0 to N, step=100<br/>分批处理避免内存溢出]
    G3 --> H3A[批处理循环<br/>i=0 to N, step=100<br/>分批处理避免内存溢出]
    G4 --> H4A[批处理循环<br/>i=0 to N, step=100<br/>分批处理避免内存溢出]

    H1A --> H1B[矩阵乘法计算<br/>torch.mm(batch, all_embeddings.t)<br/>GPU并行相似度计算]
    H2A --> H2B[矩阵乘法计算<br/>torch.mm(batch, all_embeddings.t)<br/>GPU并行相似度计算]
    H3A --> H3B[矩阵乘法计算<br/>torch.mm(batch, all_embeddings.t)<br/>GPU并行相似度计算]
    H4A --> H4B[矩阵乘法计算<br/>torch.mm(batch, all_embeddings.t)<br/>GPU并行相似度计算]

    H1B --> H1C[上三角提取<br/>只处理j > i的相似度<br/>避免重复计算]
    H2B --> H2C[上三角提取<br/>只处理j > i的相似度<br/>避免重复计算]
    H3B --> H3C[上三角提取<br/>只处理j > i的相似度<br/>避免重复计算]
    H4B --> H4C[上三角提取<br/>只处理j > i的相似度<br/>避免重复计算]

    %% 实时阈值过滤
    H1C --> I1[实时阈值过滤<br/>if cos θ >= 0.95<br/>保存相似对]
    H2C --> I2[实时阈值过滤<br/>if cos θ >= 0.95<br/>保存相似对]
    H3C --> I3[实时阈值过滤<br/>if cos θ >= 0.95<br/>保存相似对]
    H4C --> I4[实时阈值过滤<br/>if cos θ >= 0.95<br/>保存相似对]

    I1 --> I1A[内存优化<br/>不存储完整矩阵<br/>只保留相似对]
    I2 --> I2A[内存优化<br/>不存储完整矩阵<br/>只保留相似对]
    I3 --> I3A[内存优化<br/>不存储完整矩阵<br/>只保留相似对]
    I4 --> I4A[内存优化<br/>不存储完整矩阵<br/>只保留相似对]

    %% GPU到CPU数据传输
    I1A --> J[GPU到CPU数据传输<br/>相似对关系列表<br/>格式: id1,id2,score]
    I2A --> J
    I3A --> J
    I4A --> J

    %% CPU字典群组合并
    J --> K[GPU选择代表问题<br/>ID较小者作为代表<br/>representatives vs to_merge]
    K --> L[CPU字典群组合并<br/>merge_groups字典存储<br/>遍历查找实现传递性]

    %% 结果生成
    L --> M[群组代表选择<br/>选择最小ID作为代表]
    M --> N[去重结果构建<br/>主文件和映射关系]

    %% 文件输出
    N --> O1[主文件输出<br/>filename.json<br/>独立问题+群组代表]
    N --> O2[映射文件输出<br/>filename_mapping.json<br/>被合并问题详情]
    N --> O3[统计报告输出<br/>filename_report.txt<br/>合并统计信息]

    %% 样式定义
    classDef gpuNode fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef cpuNode fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef dataNode fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef outputNode fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class D1,D2,D3,D4,F1,F2,F3,F4,G1,G2,G3,G4,H1A,H1B,H1C,H2A,H2B,H2C,H3A,H3B,H3C,H4A,H4B,H4C,I1,I2,I3,I4,I1A,I2A,I3A,I4A gpuNode
    class J,K,L,M,N cpuNode
    class A,B,C,E1,E2,E3,E4 dataNode
    class O1,O2,O3 outputNode
```

## 硬件要求

### GPU配置
- **GPU型号**: 4x Tesla T4
- **显存**: 每张卡 15079MiB (~14.7GB)
- **CUDA版本**: CUDA 10.1
- **驱动版本**: NVIDIA Driver 418.67
- **GPU优先级**: cuda:1 → cuda:3 → cuda:0 → cuda:2

#### 实际硬件状态
```
Wed Jul 16 18:02:33 2025
+-----------------------------------------------------------------------------+
| NVIDIA-SMI 418.67       Driver Version: 418.67       CUDA Version: 10.1     |
|-------------------------------+----------------------+----------------------+
| GPU  Name        Persistence-M| Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp  Perf  Pwr:Usage/Cap|         Memory-Usage | GPU-Util  Compute M. |
|===============================+======================+======================|
|   0  Tesla T4            Off  | 00000000:3B:00.0 Off |                    0 |
| N/A   40C    P0    25W /  70W |   5669MiB / 15079MiB |      0%      Default |
+-------------------------------+----------------------+----------------------+
|   1  Tesla T4            Off  | 00000000:86:00.0 Off |                    0 |
| N/A   35C    P0    26W /  70W |      0MiB / 15079MiB |      0%      Default |
+-------------------------------+----------------------+----------------------+
|   2  Tesla T4            Off  | 00000000:AF:00.0 Off |                    0 |
| N/A   39C    P0    27W /  70W |   6969MiB / 15079MiB |      0%      Default |
+-------------------------------+----------------------+----------------------+
|   3  Tesla T4            Off  | 00000000:D8:00.0 Off |                    0 |
| N/A   35C    P0    26W /  70W |      0MiB / 15079MiB |      5%      Default |
+-------------------------------+----------------------+----------------------+
```

**硬件特性:**
- **总显存**: 4 × 15079MiB = 60.3GB
- **功耗**: 每卡最大70W，当前25-27W
- **温度**: 运行温度35-40°C
- **总线**: PCIe接口，独立总线ID
- **ECC**: 支持错误纠正码

### 系统要求
- **操作系统**: Linux (测试环境: CentOS/Ubuntu)
- **Python版本**: 3.9+
- **内存**: 建议32GB+
- **存储**: SSD推荐，足够空间存储输出结果

## 核心算法

### 1. 文件分配算法
```
贪心负载均衡算法:
1. 分析所有输入文件大小
2. 按问题数量排序
3. 总是分配给当前负载最轻的GPU
4. 确保负载均衡分布
```

### 2. 相似度计算 (GPU矩阵乘法算法详解)
```
GPU矩阵乘法算法 (实际实现):
1. BGE-M3模型编码文本 (GPU)
   - 输入: 文本列表 [text1, text2, ..., textN]
   - 输出: L2归一化向量矩阵 embeddings[N, 1024]
   - 批处理: batch_size=64 (硬编码), 避免GPU内存溢出

2. 批量矩阵乘法相似度计算 (GPU)
   算法核心: 使用torch.mm进行高效矩阵运算

   # L2归一化向量
   embeddings_normalized = F.normalize(embeddings_tensor, p=2, dim=1)

   for i in range(0, N, 100):  # 批处理大小硬编码为100
       batch_embeddings = embeddings_normalized[i:i+100]

       # 矩阵乘法计算整批相似度
       similarities = torch.mm(batch_embeddings, embeddings_normalized.t())

       # 上三角提取 (避免重复计算)
       for local_i in range(batch_size_actual):
           global_i = i + local_i
           if global_i + 1 < N:
               row_similarities = similarities[local_i, global_i + 1:]

               # GPU上阈值过滤
               mask = row_similarities >= threshold
               if mask.any():
                   indices = torch.nonzero(mask, as_tuple=True)[0]
                   values = row_similarities[mask]
                   # 保存相似对
                   similar_pairs.append((global_i, j, similarity))

3. 内存优化策略
   - 分批处理: batch_size=100 (硬编码)
   - 矩阵乘法: torch.mm高效GPU并行计算
   - 上三角提取: 只处理j > i的相似度
   - 实时清理: del similarities; torch.cuda.empty_cache()

4. 数学实现
   相似度计算: torch.mm(A, B.t()) 其中A, B已L2归一化
   等价于: cos(θ) = A·B (因为||A|| = ||B|| = 1)
   阈值条件: similarity ≥ 0.95 (约18.2°夹角)
```

### 3. 合并算法 (字典群组合并)
```
GPU+CPU混合算法:
1. GPU选择代表问题 (ID较小者作为代表)
2. CPU字典群组合并算法
   - 使用merge_groups字典存储群组关系
   - 遍历查找现有群组实现传递性
   - 复杂度O(n²)但结果正确
3. 生成去重结果和映射关系
4. 统计输出和文件保存

注意: 代码中存在_cpu_union_find并查集函数但未实际使用
```

## 实际算法实现详解

### 字典群组合并算法 (CPU部分)
```python
# 实际代码实现 (bge_m3_deduplication.py 第329-374行)
merge_groups = {}  # {代表问题索引: [合并的问题索引列表]}
merged_indices = set()  # 已被合并的问题索引

# 处理GPU选择的代表关系
for representative, to_merge in zip(representatives_cpu, to_merge_cpu):
    representative, to_merge = int(representative), int(to_merge)

    # 查找代表问题是否已在某个群组中 (实现传递性)
    found_group = None
    for group_rep, group_members in merge_groups.items():
        if representative in group_members or representative == group_rep:
            found_group = group_rep
            break

    # 如果找到现有群组，使用该群组的代表
    if found_group is not None:
        representative = found_group

    # 初始化群组
    if representative not in merge_groups:
        merge_groups[representative] = []

    # 添加到合并列表
    if to_merge not in merge_groups[representative] and to_merge != representative:
        merge_groups[representative].append(to_merge)
        merged_indices.add(to_merge)

# 生成最终结果
deduplicated_questions = []
mapping = {}

for i, question in enumerate(questions):
    if i not in merged_indices:  # 未被合并的问题
        deduplicated_questions.append(question)

        # 如果是群组代表，记录映射关系
        if i in merge_groups:
            merged_questions = [questions[j] for j in merge_groups[i]]
            mapping[question['id']] = {
                'kept_question': question,
                'merged_questions': merged_questions,
                'merge_count': len(merged_questions)
            }
```

### 算法特点分析
- **优点**: 实现简单，结果正确，支持传递性合并
- **缺点**: 时间复杂度O(n²)，对大数据集效率较低
- **传递性**: 通过遍历查找现有群组实现
- **代表选择**: GPU阶段已选择ID较小者作为代表

## 代码审查发现的问题

### 配置与实现不一致
1. **批处理大小硬编码**:
   - BGE-M3编码: 代码中硬编码`batch_size=64`，未使用`Config.BATCH_SIZE`
   - 相似度计算: 代码中硬编码`batch_size=100`，未使用`Config.ROWWISE_BATCH_SIZE`

2. **算法实现与描述不符**:
   - 实际使用`torch.mm`矩阵乘法，不是"逐行计算"
   - 使用L2归一化向量，相似度计算更高效
   - 批处理 + 上三角提取的混合策略

3. **GPU选择逻辑复杂**:
   - 不仅按优先级选择，还检查内存使用情况
   - 要求至少1GB可用内存才选择GPU
   - 强制GPU-only模式，无CPU回退

### 建议改进
- 使用配置文件中的批处理参数
- 统一算法描述与实际实现
- 添加配置参数的实际使用说明

## 多GPU并行处理详解

### 文件分配策略
```
1. 文件大小分析: 读取每个JSON文件，统计问题数量
2. 贪心负载均衡: 总是分配给当前负载最轻的GPU
3. GPU优先级: cuda:1 → cuda:3 → cuda:0 → cuda:2
4. 内存检查: 每个GPU至少需要1GB可用内存
5. 动态分配: 根据实际可用GPU数量调整
```

### 并行处理机制
```
1. 进程启动方式: multiprocessing.set_start_method('spawn')
   - 解决CUDA在fork进程中的初始化问题
   - 每个GPU独立进程，避免内存冲突

2. ProcessPoolExecutor: 最多4个工作进程
   - 每个进程绑定一个GPU
   - 独立的BGE_M3_Deduplicator实例
   - 异步并行处理文件

3. GPU设备绑定: torch.cuda.set_device(gpu_id)
   - 确保每个进程使用指定GPU
   - 避免GPU资源竞争
   - 独立的CUDA上下文
```

### 实际处理流程
```
主进程: 文件分析 → GPU分配 → 启动子进程
├── GPU 1进程: 处理分配的文件列表
├── GPU 3进程: 处理分配的文件列表
├── GPU 0进程: 处理分配的文件列表
└── GPU 2进程: 处理分配的文件列表

每个子进程:
1. 创建独立的BGE_M3_Deduplicator
2. 逐个处理分配的文件
3. 生成对应的输出文件
4. 完成后释放GPU资源
```

## 安装依赖

### requirements.txt
```
certifi==2025.7.14
charset-normalizer==3.4.2
click==8.1.8
filelock==3.18.0
fsspec==2025.5.1
hf-xet==1.1.5
huggingface-hub==0.10.1
idna==3.10
Jinja2==3.1.6
joblib==1.5.1
MarkupSafe==3.0.2
modelscope==1.28.0
mpmath==1.3.0
networkx==3.2.1
nltk==3.9.1
numpy==1.24.3
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
packaging==25.0
pillow==11.3.0
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.4
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.13.1
sentence-transformers==2.1.0
sentencepiece==0.2.0
sympy==1.13.1
threadpoolctl==3.6.0
tokenizers==0.12.1
torch==1.8.1+cu101
torchaudio==0.8.1
torchvision==0.9.1+cu101
tqdm==4.67.1
transformers==4.21.3
triton==3.2.0
typing_extensions==4.14.1
urllib3==2.5.0
```

### 安装命令
```bash
# 创建虚拟环境
conda create -n bge_env python=3.9
conda activate bge_env

# 安装依赖
pip install -r requirements.txt

# 验证CUDA
python -c "import torch; print(torch.cuda.is_available())"
```

## 配置说明

### config.py 主要配置
```python
# 路径配置
INPUT_PATH = "/path/to/classified/data"
OUTPUT_PATH = "/path/to/outputs"
MODEL_PATH = "/path/to/bge-m3/model"

# GPU配置
DEVICE = "cuda:1"  # 优先GPU
FALLBACK_DEVICES = ["cuda:3", "cuda:0", "cuda:2"]
FORCE_GPU_ONLY = True

# 去重配置
SIMILARITY_THRESHOLDS = [0.95]
TARGET_FILES = None  # 处理所有文件

# 性能配置 (注意: 部分参数在代码中硬编码)
BATCH_SIZE = 64  # BGE-M3编码批处理大小 (config中定义但代码硬编码)
ROWWISE_BATCH_SIZE = 100  # 相似度计算批处理 (config中定义但代码硬编码)
GPU_MAX_QUESTIONS = 5000  # GPU处理的最大问题数
```

## 使用方法

### 1. 基本使用
```bash
# 多GPU并行处理
python run_multi_gpu_deduplication.py

# 单文件测试
python bge_m3_deduplication.py
```

### 2. 数据完整性检查
```bash
# 验证处理结果
python check_data_completeness.py
```

### 3. 流程测试
```bash
# 小规模测试
python test_full_pipeline.py
```

## 输出文件说明

### 文件结构
```
outputs/
├── output_0.95/
│   ├── 地球系统.json              # 去重后的主文件
│   ├── 地球系统_mapping.json      # ID映射关系
│   └── 地球系统_report.txt        # 统计报告
```

### 输出格式
- **主文件**: 保留的独立问题 + 群组代表问题
- **映射文件**: 记录被合并问题的详细信息
- **报告文件**: 处理统计和合并群组信息

## 性能指标

### 处理能力
- **数据规模**: 支持30万+问题
- **处理速度**: 4GPU并行，约30秒/14文件(测试数据)
- **内存效率**: GPU逐行算法，避免OOM
- **准确率**: 0.95阈值下的高精度去重

### 实际测试结果
```
总问题数: 339,901
去重后数: ~238,000 (约30%去重率)
处理时间: ~2小时 (全量数据)
GPU利用率: 95%+
```

## 已知问题与TODO

### 当前限制
1. **阈值过高问题**: 0.95阈值可能导致语义相同但表达略有差异的问题未被合并

**示例问题对:**
```json
{
  "id": "34009",
  "content": "为什么天会地震？",
  "label": "地球系统"
}
```
```json
{
  "id": "36722",
  "content": "地震是什么原理？",
  "label": "地球系统"
}
```

### TODO列表

#### 1. 多轮迭代合并算法 (优先级: 高)
```python
# 实现思路
def iterative_merge(groups, threshold=0.95):
    """
    循环合并算法:
    1. 对已合并的群组进行二次相似度计算
    2. 群组间相似度 > 0.95 时进行合并
    3. 重复直到无新的合并产生
    4. 解决语义相同但表达差异的问题
    """
    while True:
        new_merges = find_group_similarities(groups, threshold)
        if not new_merges:
            break
        groups = merge_similar_groups(groups, new_merges)
    return groups
```

#### 2. 自适应阈值策略 (优先级: 中)
- 根据文本长度和复杂度动态调整阈值
- 短文本使用更严格阈值
- 长文本使用相对宽松阈值

#### 3. 语义增强算法 (优先级: 中)
- 集成多种相似度计算方法
- 结合词汇重叠度和语义相似度
- 特殊处理疑问词和关键实体

#### 4. 性能优化 (优先级: 低)
- GPU内存池管理
- 动态批处理大小调整
- 更高效的并查集实现

## 故障排除

### 常见问题

#### 1. CUDA版本不兼容
```bash
# 检查CUDA版本
nvcc --version
nvidia-smi

# 重新安装对应版本PyTorch
pip install torch==1.8.1+cu101 -f https://download.pytorch.org/whl/torch_stable.html
```

#### 2. GPU内存不足
```bash
# 减少批处理大小
BATCH_SIZE = 32
ROWWISE_BATCH_SIZE = 50

# 或启用CPU回退
FORCE_GPU_ONLY = False
```

#### 3. 模型加载失败
```bash
# 检查模型路径
ls -la /path/to/bge-m3/model/

# 重新下载模型
from sentence_transformers import SentenceTransformer
model = SentenceTransformer('BAAI/bge-m3')
model.save('/path/to/bge-m3/model/')
```

## 开发指南

### 代码结构
```
codes/
├── bge_m3_deduplication.py    # 核心去重算法
├── config.py                  # 配置文件
├── run_multi_gpu_deduplication.py  # 多GPU入口
├── check_data_completeness.py # 数据验证工具
└── test_full_pipeline.py      # 测试脚本
```

### 扩展开发
1. **新增相似度算法**: 继承`BGE_M3_Deduplicator`类
2. **自定义GPU分配**: 修改`allocate_files_to_gpus`方法
3. **输出格式扩展**: 修改`save_results`方法

## 许可证

本项目采用 MIT 许可证。

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮件联系
