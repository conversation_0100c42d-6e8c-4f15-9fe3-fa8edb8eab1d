certifi==2025.7.14
charset-normalizer==3.4.2
click==8.1.8
filelock==3.18.0
fsspec==2025.5.1
hf-xet==1.1.5
huggingface-hub==0.10.1
idna==3.10
Jinja2==3.1.6
joblib==1.5.1
MarkupSafe==3.0.2
modelscope==1.28.0
mpmath==1.3.0
networkx==3.2.1
nltk==3.9.1
numpy==1.24.3
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
packaging==25.0
pillow==11.3.0
pip==25.1
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.4
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.13.1
sentence-transformers==2.1.0
sentencepiece==0.2.0
setuptools==78.1.1
sympy==1.13.1
threadpoolctl==3.6.0
tokenizers==0.12.1
torch==1.8.1+cu101
torchaudio==0.8.1
torchvision==0.9.1+cu101
tqdm==4.67.1
transformers==4.21.3
triton==3.2.0
typing_extensions==4.14.1
urllib3==2.5.0
wheel==0.45.1
