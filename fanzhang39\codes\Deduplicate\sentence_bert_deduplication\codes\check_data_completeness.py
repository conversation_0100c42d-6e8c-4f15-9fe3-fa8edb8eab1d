"""
数据完整性检查脚本
检查源数据是否被输出数据完全包含
"""

import os
import json
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('check_data_completeness.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def load_json_file(filepath):
    """加载JSON文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"加载文件失败 {filepath}: {e}")
        return None

def extract_ids_from_source(data):
    """从源数据中提取所有ID"""
    if isinstance(data, list):
        return {str(item['id']) for item in data if 'id' in item}
    return set()

def extract_ids_from_output(deduplicated_data, mapping_data):
    """从输出数据中提取所有ID"""
    all_ids = set()
    
    # 从去重后的主文件中提取ID
    if isinstance(deduplicated_data, list):
        for item in deduplicated_data:
            if 'id' in item:
                all_ids.add(str(item['id']))
    
    # 从mapping文件中提取被合并的问题ID
    if isinstance(mapping_data, dict):
        for key, value in mapping_data.items():
            if isinstance(value, dict):
                # 被合并的问题ID
                if 'merged_questions' in value:
                    for merged_q in value['merged_questions']:
                        if 'id' in merged_q:
                            all_ids.add(str(merged_q['id']))
    
    return all_ids

def check_file_completeness(source_file, output_dir, filename_base):
    """检查单个文件的完整性"""
    logger = logging.getLogger(__name__)
    
    # 文件路径
    deduplicated_file = os.path.join(output_dir, f"{filename_base}.json")
    mapping_file = os.path.join(output_dir, f"{filename_base}_mapping.json")
    report_file = os.path.join(output_dir, f"{filename_base}_report.txt")
    
    logger.info(f"\n=== 检查文件: {filename_base} ===")
    
    # 检查文件是否存在
    missing_files = []
    if not os.path.exists(source_file):
        logger.error(f"源文件不存在: {source_file}")
        return False
    
    if not os.path.exists(deduplicated_file):
        missing_files.append("去重文件")
    if not os.path.exists(mapping_file):
        missing_files.append("映射文件")
    if not os.path.exists(report_file):
        missing_files.append("报告文件")
    
    if missing_files:
        logger.error(f"缺失文件: {', '.join(missing_files)}")
        return False
    
    # 加载数据
    source_data = load_json_file(source_file)
    deduplicated_data = load_json_file(deduplicated_file)
    mapping_data = load_json_file(mapping_file)
    
    if None in [source_data, deduplicated_data, mapping_data]:
        logger.error("数据加载失败")
        return False
    
    # 提取ID
    source_ids = extract_ids_from_source(source_data)
    output_ids = extract_ids_from_output(deduplicated_data, mapping_data)
    
    # 统计信息
    logger.info(f"源文件问题数量: {len(source_ids)}")
    logger.info(f"去重后问题数量: {len(deduplicated_data)}")
    logger.info(f"输出总ID数量: {len(output_ids)}")
    
    # 逐个ID验证完整性
    logger.info("开始逐个ID验证...")

    missing_ids = []
    extra_ids = []
    verified_count = 0

    # 检查源文件中的每个ID是否在输出中存在
    for source_id in source_ids:
        if source_id in output_ids:
            verified_count += 1
        else:
            missing_ids.append(source_id)

    # 检查输出中是否有额外的ID
    for output_id in output_ids:
        if output_id not in source_ids:
            extra_ids.append(output_id)

    logger.info(f"ID验证完成: {verified_count}/{len(source_ids)} 个ID验证通过")

    if missing_ids:
        logger.error(f"❌ 缺失的ID数量: {len(missing_ids)}")
        if len(missing_ids) <= 20:
            logger.error(f"缺失的ID: {sorted(missing_ids)}")
        else:
            sample_missing = sorted(missing_ids)[:20]
            logger.error(f"缺失的ID样本: {sample_missing} (还有{len(missing_ids)-20}个)")
        return False

    if extra_ids:
        logger.warning(f"⚠️ 额外的ID数量: {len(extra_ids)}")
        if len(extra_ids) <= 20:
            logger.warning(f"额外的ID: {sorted(extra_ids)}")
        else:
            sample_extra = sorted(extra_ids)[:20]
            logger.warning(f"额外的ID样本: {sample_extra} (还有{len(extra_ids)-20}个)")

    # 读取报告文件统计信息
    try:
        with open(report_file, 'r', encoding='utf-8') as f:
            report_content = f.read()
        
        # 提取统计信息
        lines = report_content.split('\n')
        for line in lines:
            if '原始问题数量' in line or '去重后问题数量' in line or '合并群组数量' in line:
                logger.info(f"报告统计: {line.strip()}")
    
    except Exception as e:
        logger.warning(f"读取报告文件失败: {e}")
    
    logger.info(f"✅ {filename_base} 数据完整性检查通过")
    return True

def check_all_files():
    """检查所有文件的完整性"""
    logger = logging.getLogger(__name__)
    
    source_dir = "/work1/data/fanzhang39/share/kexue/wjp/Type13/deduplication/data/classified"
    output_dir = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/outputs/outputs/output_0.95"
    
    logger.info("=== 开始数据完整性检查 ===")
    logger.info(f"源数据目录: {source_dir}")
    logger.info(f"输出数据目录: {output_dir}")
    
    # 检查目录是否存在
    if not os.path.exists(source_dir):
        logger.error(f"源数据目录不存在: {source_dir}")
        return False
    
    if not os.path.exists(output_dir):
        logger.error(f"输出数据目录不存在: {output_dir}")
        return False
    
    # 获取所有JSON文件
    source_files = [f for f in os.listdir(source_dir) if f.endswith('.json')]
    if not source_files:
        logger.error("源目录中没有找到JSON文件")
        return False
    
    logger.info(f"找到 {len(source_files)} 个源文件")
    
    # 检查每个文件
    results = {}
    for json_file in sorted(source_files):
        filename_base = json_file[:-5]  # 去掉.json后缀
        source_file = os.path.join(source_dir, json_file)
        
        try:
            result = check_file_completeness(source_file, output_dir, filename_base)
            results[filename_base] = result
        except Exception as e:
            logger.error(f"检查 {filename_base} 时出错: {e}")
            results[filename_base] = False
    
    # 输出总结
    logger.info("\n=== 检查结果总结 ===")
    passed_count = 0
    failed_count = 0
    
    for filename, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{filename}: {status}")
        if result:
            passed_count += 1
        else:
            failed_count += 1
    
    logger.info(f"\n总计: {passed_count} 个通过, {failed_count} 个失败")
    
    if failed_count == 0:
        logger.info("🎉 所有文件数据完整性检查通过！")
        return True
    else:
        logger.error(f"⚠️ 有 {failed_count} 个文件存在数据完整性问题")
        return False

def detailed_id_verification(source_file, output_dir, filename_base):
    """详细的ID逐一验证"""
    logger = logging.getLogger(__name__)

    logger.info(f"\n=== 详细ID验证: {filename_base} ===")

    # 文件路径
    deduplicated_file = os.path.join(output_dir, f"{filename_base}.json")
    mapping_file = os.path.join(output_dir, f"{filename_base}_mapping.json")

    # 加载数据
    source_data = load_json_file(source_file)
    deduplicated_data = load_json_file(deduplicated_file)
    mapping_data = load_json_file(mapping_file)

    if None in [source_data, deduplicated_data, mapping_data]:
        logger.error("数据加载失败")
        return False

    # 提取所有ID
    source_ids = extract_ids_from_source(source_data)
    output_ids = extract_ids_from_output(deduplicated_data, mapping_data)

    # 构建详细的ID映射
    main_file_ids = set()
    if isinstance(deduplicated_data, list):
        for item in deduplicated_data:
            if 'id' in item:
                main_file_ids.add(str(item['id']))

    merged_ids = set()
    if isinstance(mapping_data, dict):
        for key, value in mapping_data.items():
            if isinstance(value, dict) and 'merged_questions' in value:
                for merged_q in value['merged_questions']:
                    if 'id' in merged_q:
                        merged_ids.add(str(merged_q['id']))

    logger.info(f"源文件ID总数: {len(source_ids)}")
    logger.info(f"主文件ID数量: {len(main_file_ids)}")
    logger.info(f"被合并ID数量: {len(merged_ids)}")

    # 逐个验证所有ID（完整验证）
    source_ids_list = sorted(list(source_ids))

    logger.info(f"开始验证所有 {len(source_ids)} 个ID...")

    verified_in_main = 0
    verified_in_merged = 0
    missing_ids = []

    # 验证每一个源文件ID
    for i, source_id in enumerate(source_ids_list):
        if source_id in main_file_ids:
            verified_in_main += 1
        elif source_id in merged_ids:
            verified_in_merged += 1
        else:
            missing_ids.append(source_id)
            logger.error(f"缺失ID: {source_id}")

        # 每验证1000个ID显示一次进度
        if (i + 1) % 1000 == 0:
            logger.info(f"验证进度: {i + 1}/{len(source_ids)} ({(i + 1) / len(source_ids) * 100:.1f}%)")

    logger.info(f"验证完成: {verified_in_main} 个在主文件, {verified_in_merged} 个被合并, {len(missing_ids)} 个缺失")

    if missing_ids:
        logger.error(f"❌ 发现 {len(missing_ids)} 个缺失ID")
        logger.error(f"缺失ID总数: {len(missing_ids)}")
        return False
    else:
        logger.info(f"✅ 所有 {len(source_ids)} 个ID验证全部通过")
        return True

def check_all_files_detailed():
    """对所有文件进行详细的完整ID验证"""
    logger = logging.getLogger(__name__)

    source_dir = "/work1/data/fanzhang39/share/kexue/wjp/Type13/deduplication/data/classified"
    output_dir = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/outputs/outputs_7_16/output_0.95"

    logger.info("\n=== 所有文件详细ID验证 ===")

    # 获取所有JSON文件
    source_files = [f for f in os.listdir(source_dir) if f.endswith('.json')]
    if not source_files:
        logger.error("源目录中没有找到JSON文件")
        return False

    logger.info(f"开始对 {len(source_files)} 个文件进行完整ID验证")

    detailed_results = {}

    for json_file in sorted(source_files):
        filename_base = json_file[:-5]  # 去掉.json后缀
        source_file = os.path.join(source_dir, json_file)

        logger.info(f"\n{'='*60}")
        logger.info(f"详细验证文件: {filename_base}")
        logger.info(f"{'='*60}")

        try:
            # 详细ID验证
            result = detailed_id_verification(source_file, output_dir, filename_base)
            detailed_results[filename_base] = result
        except Exception as e:
            logger.error(f"详细验证 {filename_base} 时出错: {e}")
            detailed_results[filename_base] = False

    # 输出详细验证总结
    logger.info(f"\n{'='*60}")
    logger.info("详细验证结果总结")
    logger.info(f"{'='*60}")

    passed_count = 0
    failed_count = 0

    for filename, result in detailed_results.items():
        status = "✅ 完全通过" if result else "❌ 有缺失"
        logger.info(f"{filename}: {status}")
        if result:
            passed_count += 1
        else:
            failed_count += 1

    logger.info(f"\n详细验证总计: {passed_count} 个完全通过, {failed_count} 个有缺失")

    return failed_count == 0

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=== BGE-M3 数据完整性检查工具 ===")
    
    try:
        # 基本完整性检查
        basic_passed = check_all_files()

        # 详细ID验证（所有文件）
        detailed_passed = check_all_files_detailed()

        # 最终结果
        if basic_passed and detailed_passed:
            logger.info("\n🎉 所有文件数据完整性检查全部通过！")
            logger.info("每个源文件的每个ID都被正确包含在输出结果中。")
            logger.info("数据处理完全正确，无任何丢失。")
        else:
            logger.error("\n❌ 发现数据完整性问题！")
            if not basic_passed:
                logger.error("基本完整性检查失败")
            if not detailed_passed:
                logger.error("详细ID验证失败")

        return basic_passed and detailed_passed
        
    except Exception as e:
        logger.error(f"检查过程中出现异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
