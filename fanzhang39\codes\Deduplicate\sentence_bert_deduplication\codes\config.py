"""
BGE-M3文本去重系统配置文件
"""

import os
from pathlib import Path

class Config:
    """配置类"""
    
    # 路径配置
    MODEL_PATH = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/model"
    INPUT_PATH = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/datas/data_round2"
    OUTPUT_PATH = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/outputs/outputs_7_16_round2"
    
    # 模型配置
    MAX_LENGTH = 512
    BATCH_SIZE = 64  # BGE-M3编码批处理大小
    # GPU配置
    PREFERRED_GPU_ORDER = [1, 3, 0, 2]  # GPU优先级顺序
    DEFAULT_GPU_ID = PREFERRED_GPU_ORDER[0]  # 自动使用最高优先级GPU作为默认
    FORCE_GPU_ONLY = True  # 强制只使用GPU，不回退到CPU

    # 多GPU并行处理配置
    MIN_GPU_MEMORY_GB = 3.0  # 多GPU处理时每个GPU的最小内存要求(GB)

    # 去重配置 - 只使用0.95阈值
    SIMILARITY_THRESHOLDS = [0.95]

    # 指定处理的文件列表 - 处理所有数据文件
    TARGET_FILES = None  # None表示处理所有JSON文件

    # 逐行计算配置
    ROWWISE_BATCH_SIZE = 100  # 逐行计算的批次大小
    GPU_MAX_QUESTIONS = 5000  # GPU处理的最大问题数
    
    # 文本处理配置
    MIN_TEXT_LENGTH = 2  # 最小文本长度
    MAX_TEXT_LENGTH = 500  # 最大文本长度
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "bge_m3_deduplication.log"
    
    # 性能配置
    ENABLE_PROGRESS_BAR = True
    NORMALIZE_EMBEDDINGS = True
    
    @classmethod
    def validate_paths(cls):
        """验证路径配置"""
        if not os.path.exists(cls.MODEL_PATH):
            raise FileNotFoundError(f"模型路径不存在: {cls.MODEL_PATH}")
        
        if not os.path.exists(cls.INPUT_PATH):
            raise FileNotFoundError(f"输入路径不存在: {cls.INPUT_PATH}")
        
        # 创建输出目录
        os.makedirs(cls.OUTPUT_PATH, exist_ok=True)
    
    @classmethod
    def get_device(cls):
        """获取最优计算设备 - 强制GPU-only模式"""
        import torch

        if not torch.cuda.is_available():
            raise RuntimeError("CUDA不可用，但配置为GPU-only模式")

        if cls.DEVICE.startswith("cuda:"):
            # 检查指定GPU是否可用
            try:
                device_id = int(cls.DEVICE.split(":")[1])
                if device_id < torch.cuda.device_count():
                    return cls.DEVICE
            except:
                pass

        # 尝试备用GPU设备
        for device in cls.FALLBACK_DEVICES:
            try:
                device_id = int(device.split(":")[1])
                if device_id < torch.cuda.device_count():
                    return device
            except:
                continue

        # 如果所有指定GPU都不可用，使用第一个可用GPU
        if torch.cuda.device_count() > 0:
            return "cuda:0"

        # 如果没有GPU可用，抛出错误
        raise RuntimeError("没有可用的GPU设备，但配置为GPU-only模式")

    @classmethod
    def get_gpu_memory_info(cls):
        """获取GPU内存信息"""
        import torch
        if not torch.cuda.is_available():
            return {}

        gpu_info = {}
        for i in range(torch.cuda.device_count()):
            try:
                torch.cuda.set_device(i)
                total = torch.cuda.get_device_properties(i).total_memory / 1024**3  # GB
                allocated = torch.cuda.memory_allocated(i) / 1024**3  # GB
                free = total - allocated
                gpu_info[f"cuda:{i}"] = {
                    "total": total,
                    "allocated": allocated,
                    "free": free,
                    "usage_percent": (allocated / total) * 100
                }
            except:
                continue
        return gpu_info

# 预定义的标签文件列表（可根据实际情况调整）
EXPECTED_LABELS = [
    "地球系统",
    "生物多样性", 
    "物质科学",
    "技术工程",
    "宇宙演化",
    "生命科学",
    "数学建模",
    "信息技术",
    "环境保护",
    "能源利用",
    "材料科学",
    "人工智能",
    "医学健康",
    "社会科学"
]

# 文件格式配置
JSON_CONFIG = {
    "ensure_ascii": False,
    "indent": 2,
    "separators": (',', ': ')
}

# 报告模板配置
REPORT_TEMPLATE = """BGE-M3文本去重统计报告
生成时间: {timestamp}
文件名: {filename}
相似度阈值: {threshold}

=== 去重统计 ===
原始问题数量: {original_count}
去重后数量: {deduplicated_count}
合并问题数量: {merged_count}
合并比例: {merge_ratio:.2f}%

=== 合并群组统计 ===
合并群组数量: {group_count}
最大合并群组大小: {max_group_size}
平均合并群组大小: {avg_group_size:.2f}

=== 处理信息 ===
处理时间: {processing_time:.2f}秒
使用设备: {device}
模型路径: {model_path}

=== 详细合并信息 ===
{detailed_info}
"""
