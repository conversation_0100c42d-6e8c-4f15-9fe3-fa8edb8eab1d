#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取生物类问答数据脚本
从seed_1.6_qa_out.json中提取所有标签为"生物"的记录
"""

import json
import os
from typing import List, Dict, Any


def load_json_data(file_path: str) -> List[Dict[str, Any]]:
    """
    加载JSON数据文件
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        解析后的JSON数据列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载数据，共 {len(data)} 条记录")
        return data
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except json.JSONDecodeError as e:
        print(f"错误：JSON解析失败 - {e}")
        return []
    except Exception as e:
        print(f"错误：加载文件失败 - {e}")
        return []


def extract_biology_data(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    提取标签为"生物"的记录
    
    Args:
        data: 原始数据列表
        
    Returns:
        筛选后的生物类数据列表
    """
    biology_data = []
    
    for item in data:
        if item.get('label') == '生物':
            biology_data.append(item)
    
    print(f"提取到 {len(biology_data)} 条生物类记录")
    return biology_data


def save_biology_data(biology_data: List[Dict[str, Any]], output_path: str) -> bool:
    """
    保存生物类数据到新文件
    
    Args:
        biology_data: 生物类数据列表
        output_path: 输出文件路径
        
    Returns:
        保存是否成功
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(biology_data, f, ensure_ascii=False, indent=2)
        print(f"生物类数据已保存到: {output_path}")
        return True
    except Exception as e:
        print(f"错误：保存文件失败 - {e}")
        return False


def print_statistics(biology_data: List[Dict[str, Any]]) -> None:
    """
    打印生物类数据的统计信息
    
    Args:
        biology_data: 生物类数据列表
    """
    print("\n=== 生物类数据统计 ===")
    print(f"总记录数: {len(biology_data)}")
    
    # 统计问题类型
    question_types = {}
    for item in biology_data:
        content = item.get('content', '')
        if content.startswith('为什么'):
            question_types['为什么类'] = question_types.get('为什么类', 0) + 1
        elif content.startswith('什么是') or content.startswith('什么叫'):
            question_types['什么是类'] = question_types.get('什么是类', 0) + 1
        elif content.startswith('怎么') or content.startswith('如何'):
            question_types['怎么类'] = question_types.get('怎么类', 0) + 1
        else:
            question_types['其他类'] = question_types.get('其他类', 0) + 1
    
    print("\n问题类型分布:")
    for q_type, count in question_types.items():
        print(f"  {q_type}: {count} 条")
    
    # 显示前10个问题示例
    print("\n前10个问题示例:")
    for i, item in enumerate(biology_data[:10], 1):
        print(f"  {i}. {item.get('content', '')}")


def main():
    """主函数"""
    # 文件路径配置
    input_file = "D:\\ProJects\\kexue\\download\\0710\\seed_1.6_qa_out.json"
    output_file = "D:\\ProJects\\kexue\\fanzhang39\\codes\\qa\\k-means\\data_biology_only\\biology_qa_data.json"
    
    print("=== 生物类问答数据提取工具 ===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("-" * 50)
    
    # 1. 加载原始数据
    data = load_json_data(input_file)
    if not data:
        return
    
    # 2. 提取生物类数据
    biology_data = extract_biology_data(data)
    if not biology_data:
        print("未找到生物类数据")
        return
    
    # 3. 保存提取的数据
    success = save_biology_data(biology_data, output_file)
    if not success:
        return
    
    # 4. 打印统计信息
    print_statistics(biology_data)
    
    print("\n=== 提取完成 ===")


if __name__ == "__main__":
    main()
