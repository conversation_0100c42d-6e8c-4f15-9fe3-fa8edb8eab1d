
import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys

fewshot_input_1 = {
    "question": "运动是什么？",
    "analysis": {
        "核心科学原理": "运动是物体位置随时间的变化，包括直线运动、曲线运动等多种形式。",
        "实验设计": {
            "实验": "观察运动实验 (材料：小车 + 轨道):\n1. 将小车放在轨道一端。\n2. 轻轻推动小车，让它在轨道上运动。\n3. 观察小车的运动路径。",
            "安全提示": "推动小车时不要用力过猛，防止小车滑落伤人。"
        },
        "居里夫人": {
            "关键现象解释": "就像我们推动一个小球，小球从一个位置移动到另一个位置，这就是小球在做运动。",
            "趣味延伸": {
                "小行动": "在操场上观察跑步的同学，看看他们是直线运动还是曲线运动。",
                "冷知识": "地球也在不停地运动，它不仅自转，还围绕太阳公转。"
            }
        },
        "达尔文": {
            "关键现象解释": "比如动物在奔跑、飞翔，它们的位置在不断改变，这就是动物的运动。",
            "趣味延伸": {
                "小行动": "观察蚂蚁搬家，看看蚂蚁是怎样运动的。",
                "冷知识": "有些植物也会运动，比如向日葵会随着太阳转动。"
            }
        }
    }
}
fewshot_output_1 = {
    "居里夫人_小行动": "简单来说，运动就是物体的位置随着时间发生了改变，它其实无处不在。就像我们轻轻推动一个小球，能清晰看到它从原来的位置滚动到另一个新的位置。我们可以自己动手做一个更有趣的小实验来感受：在轨道上放一辆小车，轻轻给它一个力（注意安全！），然后仔细观察它滑动的完整路径。课后你还可以去操场上仔细观察跑步的同学，思考他们身体的移动方式，以及运动轨迹是直线还是曲线。",
    "居里夫人_冷知识": "科学上讲，运动是物体位置随时间的变化，从微小的粒子到宏伟的星球都在不停运动。就像推动一个小球，它会从一个地方滚到另一个地方，非常直观。你也可以自己动手，在轨道上轻轻推动小车来观察这个过程（注意安全哦！）。而一个有趣的冷知识是，我们脚下看似静止的地球，其实也一直在高速运动，它不仅自己旋转，还围绕着太阳不知疲倦地奔跑呢！",
    "达尔文_小行动": "运动就是物体位置随时间的变化，这在自然界中随处可见。比如，猎豹在草原上飞速奔跑，鸟儿在天空中自由飞翔，它们的位置都在不断改变，展现了生命的力量。我们可以用小车和轨道做一个安全的模拟实验，轻轻推动小车，观察它的移动路径。做完实验，你再去操场上观察一下跑步的同学，他们的运动方式和动物的奔跑，是不是有许多奇妙的共同点呢？",
    "达尔文_冷知识": "从科学角度看，运动是物体位置随时间的变化，是宇宙中的普遍规律。自然界中，动物的奔跑和飞翔就是生动的例子，充满了节奏与美感。我们也可以用小车和轨道模拟这个过程（注意安全！），观察物体是如何运动的。不仅如此，一个更宏大的运动正在发生：我们赖以生存的地球，其实也是一个运动高手，它正带着我们不停地自转，同时还围绕着太阳公转。"
}
fewshot_input_2 =     {
    "question": "为什么天空会出现彩虹？",
    "analysis": {
        "核心科学原理": "太阳光中包含不同颜色的光，当太阳光穿过三棱镜或类似三棱镜的介质（如小水滴）时，不同颜色的光会发生不同程度的折射，从而形成彩色光带，即彩虹。",
        "实验设计": {
            "实验": "模拟彩虹实验 (材料：喷壶 + 阳光充足的地方):\n1. 背对着太阳站立。\n2. 用喷壶朝空中喷水雾。\n3. 观察在水雾中是否出现类似彩虹的彩色光带。",
            "安全提示": "注意不要将水喷到眼睛里，选择合适的场地避免滑倒。"
        },
        "居里夫人": {
            "关键现象解释": "雨后，空气中悬浮着大量小水滴，这些小水滴就像一个个三棱镜，太阳光进入水滴后，不同颜色的光被折射出来，就形成了彩虹。",
            "趣味延伸": {
                "小行动": "在有阳光的日子，用喷壶在家里的院子或阳台上制造小彩虹，和小伙伴一起观察。",
                "冷知识": "在飞机上有时能看到完整的圆形彩虹，因为从飞机上看，没有地面的阻挡，彩虹就呈现出完整的圆形。"
            }
        },
        "达尔文": {
            "关键现象解释": "彩虹的出现和大气中的水汽有关，在一些潮湿的森林里，偶尔也能看到彩虹，这是因为森林中的水汽和阳光相互作用产生的。",
            "趣味延伸": {
                "小行动": "寻找大自然中可能出现彩虹的地方，如瀑布附近，去观察和记录彩虹的样子。",
                "冷知识": "在古代，人们认为彩虹是神的桥梁，连接着天地，有着美好的寓意。"
            }
        }
    }
}
fewshot_output_2 ={
        "居里夫人_小行动": "天空出现彩虹，是因为太阳光被空气中的小水滴折射，分解成了彩色的光带，就像光线穿过三棱镜一样。雨后，空气中悬浮的大量小水滴就扮演了三棱镜的角色。想亲眼看看吗？我们可以自己动手模拟：背对着太阳，用喷壶朝空中喷出水雾（注意安全！），就能制造出属于你自己的迷你彩虹，快和小伙伴一起试试吧。",
        "居里夫人_冷知识": "彩虹是太阳光穿过空气中小水滴时发生的折射现象，这些小水滴如同无数个微小的三棱镜，将阳光分解成七色光芒。我们可以背对太阳喷洒水雾来模拟这个过程（注意安全！），亲手制造一道彩虹。更有趣的是，我们平时看到的彩虹都是弧形，但如果从飞机上向下看，就有机会看到一整个圆形的彩虹，因为它不再被地面遮挡。",
        "达尔文_小行动": "彩虹是阳光穿过空气中的水汽时，因折射而形成的彩色光带。这种现象不仅在雨后出现，在一些水汽充沛的潮湿森林里也能见到。我们可以做一个简单的模拟实验：在阳光下，背对太阳喷洒水雾（注意安全！），观察光的色散。如果你想看更壮观的天然彩虹，可以去瀑布附近等水雾弥漫的地方寻找，并用相机记录下这美丽的瞬间。",
        "达尔文_冷知识": "彩虹的出现，是阳光和大气中水汽相互作用的结果，当光线穿过这些小水滴介质时就会发生折射，形成彩色光带。你知道吗？在科学揭示原理之前，古人无法解释彩虹的成因，常常认为它是连接天地的神之桥梁，赋予了它许多浪漫而美好的神话寓意。我们也可以用喷壶制造水雾来模拟这个奇妙的现象（注意安全！）。"
}
fewshot_input_3 = {
        "question": "为什么世界上会有人。",
        "analysis": {
            "核心科学原理": "人类是生物进化的结果。在漫长的时间里，生物不断适应环境变化，经过遗传、变异和自然选择等过程，逐渐从简单到复杂、从低级到高级进化，最终演化出了人类。",
            "实验设计": {
                "实验": "",
                "安全提示": ""
            },
            "居里夫人": {
                "关键现象解释": "就像物质在不同的物理条件下会发生变化一样，地球上的生物在不同的环境条件下，其身体结构和特征也会慢慢改变。经过很长时间的变化和积累，就有了人类。",
                "趣味延伸": {
                    "小行动": "",
                    "冷知识": "人类的大脑在进化过程中变得越来越大，现在人类大脑的重量大约占身体重量的2%，但却消耗了身体约20%的能量。"
                }
            },
            "达尔文": {
                "关键现象解释": "在远古时期，地球上生活着很多种类的生物。一些古猿因为环境变化，从树上下到地面生活。为了适应新环境，它们逐渐学会了直立行走、使用工具等，经过一代又一代的演变，就进化成了人类。",
                "趣味延伸": {
                    "小行动": "和小伙伴一起收集不同时期人类化石的图片，比较它们的形态差异。",
                    "冷知识": "在人类进化过程中，曾经出现过很多不同的人种，比如尼安德特人，但只有智人这一人种存活了下来。"
                }
            }
    }
}
fewshot_output_3 = {
        "居里夫人_小行动": "",
        "居里夫人_冷知识": "人类的出现是生物进化的奇妙结果。就像物质在不同物理条件下会发生变化，地球生物也在漫长的时间里，为适应环境而慢慢改变自身。经过无数代的积累和演变，最终才有了我们人类。一个有趣的知识是：人类的大脑在进化中变得越来越大，虽然只占体重的2%，却要消耗身体将近20%的能量，是个“高能耗”的器官呢！",
        "达尔文_小行动": "人类是生物长期进化的产物。很久以前，部分古猿因环境变化从树上到地面生活，在适应过程中，它们逐渐学会直立行走、使用工具，经过代代相传的演变，最终进化成了人类。想更直观地感受这个过程吗？你可以和小伙伴一起，收集不同时期人类祖先的化石图片，仔细比较一下他们的头骨和体型有什么奇妙的差异。",
        "达尔文_冷知识": "人类的诞生源于伟大的生物进化。在远古时代，一些古猿为了适应变化的环境，从树栖转向地面生活，并逐渐学会直立行走与使用工具，最终演变成了人类。你知道吗？在漫长的进化史中，地球上曾出现过多个“兄弟”人种，比如强壮的尼安德特人。但经过自然选择的考验，最终只有我们智人这一支延续了下来。"
    }
fewshot_input_4 = {
            "question": "水银是什么？",
            "analysis": {
                "核心科学原理": "水银是一种金属，也叫汞，在常温常压下是液态的，具有特殊的物理性质。",
                "实验设计": {
                    "实验": "",
                    "安全提示": ""
                },
                "居里夫人": {
                    "关键现象解释": "水银在一些温度计里能看到，它会随着温度变化而热胀冷缩，显示出不同的刻度。",
                    "趣味延伸": {
                        "小行动": "",
                        "冷知识": "水银的密度很大，比很多常见金属都重，一升水银的重量超过13千克呢！"
                    }
                },
                "达尔文": {
                    "关键现象解释": "在自然界中，如果有一些含汞的矿石被风化，水银可能会慢慢渗出到周围环境中。",
                    "趣味延伸": {
                        "小行动": "",
                        "冷知识": "有些鱼体内可能会积累水银，所以小朋友不能吃太多大型的海鱼哦。"
                    }
                }
            }
}
fewshot_output_4 =  {
        "居里夫人_小行动": "",
        "居里夫人_冷知识": "水银，科学上叫汞，是一种非常特别的金属，它在常温下是银白色的液体。你可能在一些老式温度计里见过它，它会随着温度热胀冷缩，从而指示出准确的读数。这里有个关于它的冷知识：水银的密度非常大，是水的13倍多！这意味着，一小瓶看起来和水差不多的水银，拿在手里却会感觉异常沉重，非常奇妙。",
        "达尔文_小行动": "",
        "达尔文_冷知识": "水银是一种在常温下呈现液态的特殊金属，也叫汞。在自然界中，它有时会从一些含汞的矿石中慢慢渗出，进入到周围的环境里。因此，它可能会通过食物链进行富集，比如在一些大型海鱼的体内积累。所以，为了身体健康，科学家们会提醒我们，尤其是小朋友，不能一次性吃太多像金枪鱼这样的大型深海鱼哦。"
    }


def build_prompt(input):
    return f"""
#任务描述
你是一位顶级的少儿科普内容专家和专业的科学知识内容整合与精炼专家。尤其擅长将复杂、抽象的科学知识点，通过生动有趣、通俗易懂的方式，转化为适合青少年和充满好奇心的学习者阅读的科普解说。你的文字富有启发性，总能点燃读者的求知欲。你的任务是根据所提供的JSON数据，遵循四种不同的内容组合规则，生成四段独立、精简且通俗易懂的科普回答。

#核心任务:
1.  **输入格式**: 你将收到一个包含特定科学问题“question”、原理分析“analysis”、多角度解读等字段的JSON对象。
2.  **输出要求**: 你必须严格按照下面的“四种答案配方”生成四个答案。用键名为`answer`的字段。输出结果必须清晰地在"answer"字段中标记包含"居里夫人_小行动"、"居里夫人_冷知识"、"达尔文_小行动"、"达尔文_冷知识"字段
3.  **内容来源**: 每个答案都必须融合相应配方中指定的所有字段内容，不得遗漏。
4.  **语言风格**: 答案应保持科普风格，生动有趣，易于理解。

#四种答案配方:
你必须生成四个答案，每个答案的键名和其内容来源配方严格对应如下：
1.  `"居里夫人_小行动":`
    * **内容来源:** `analysis.核心科学原理` + `analysis.实验设计` + `居里夫人.关键现象解释` + `居里夫人.趣味延伸.小行动`

2.  `"居里夫人_冷知识":`
    * **内容来源:** `analysis.核心科学原理` + `analysis.实验设计` + `居里夫人.关键现象解释` + `居里夫人.趣味延伸.冷知识`

3.  `"达尔文_小行动":`
    * **内容来源:** `analysis.核心科学原理` + `analysis.实验设计` + `达尔文.关键现象解释` + `达尔文.趣味延伸.小行动`

4.  `"达尔文_冷知识":`
    * **内容来源:** `analysis.核心科学原理` + `analysis.实验设计` + `达尔文.关键现象解释` + `达尔文.趣味延伸.冷知识`

# 关键约束条件 (必须严格遵守)

1.  **空值处理规则 (最高优先级)**: 在为某个答案（如 “居里夫人_小行动”）生成内容**之前**，必须检查其配方中对应的 `趣味延伸` 部分（如 `居里夫人.趣味延伸.小行动`）是否为**空字符串 `""`**。
    * **如果该字段为空**，则最终 `answer` 对象中对应的键（“居里夫人_小行动”）的值也**必须是空字符串 `""`**，并跳过该答案的文本生成。
    * 此规则对所有四个答案配方均适用。
2.  **内容完整性**: 必须融合配方中指定的所有信息模块，不得随意增减或遗漏。
3.  **移除问题**: 生成的所有答案文本中，**绝对不能包含**原始的`question`字段内容。答案应直接切入解释。
4.  **字数控制**: 每段答案的最终文本长度**必须严格控制在120至130个字符之间（包含所有中英文标点符号）**。
5.  **语言风格**:
    * **通俗生动**: 仿佛在对一个聪明的孩子讲解，避免使用生僻术语。
    * **多用比喻**: 善于运用生活中的常见事物进行类比，帮助理解。
    * **启发互动**: 语言富有启发性，鼓励读者亲身观察或动手尝试。

---
#样例1_完整
##输入
{fewshot_input_1}
##输出
{fewshot_output_1}

#样例2_完整
##输入
{fewshot_input_2}
##输出
{fewshot_output_2}

#样例3_缺值
##输入
{fewshot_input_3}
##输出
{fewshot_output_3}

#样例3_缺值
##输入
{fewshot_input_4}
##输出
{fewshot_output_4}

**[输入JSON数据]:**
{input}

"""


def convert(line):
    return json.loads(line)


if __name__ == '__main__':
    data_path = 'D:\\ProJects\\kexue\\crawl_data\\0722\\sci_normal_clean.json'
    save_file = 'D:\\ProJects\\kexue\\crawl_data\\0722\\build_prompt_output\\sci4answers.json'

    total = 0
    res = []
    f = open(data_path, encoding='utf-8')
    with Pool(32) as p:
        all_data = p.imap(convert, f)
        # print(len(all_data))
        for data in all_data:
            total += 1
            if data:
                d = {}
                d['id'] = total
                d['query'] = build_prompt({"content": data}).strip()
                res.append(d)

    with open(save_file, 'w', encoding='utf-8') as f:
        for item in res:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

