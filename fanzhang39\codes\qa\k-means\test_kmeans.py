#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通用语义匹配聚类算法
"""

import json
import numpy as np
from biology_kmeans_analysis import BiologyQAClusterAnalyzer, SemanticMatchingConfig
import biology_kmeans_analysis

print(f"当前加载的通用语义匹配分析文件路径: {biology_kmeans_analysis.__file__}")

def create_test_data():
    """创建精确匹配测试数据"""
    test_data = [
        # 组1：做梦相关（应该聚在一起）
        {"id": "1", "content": "人为什么会做梦？", "label": "生物"},
        {"id": "2", "content": "为什么人会做梦？", "label": "生物"},
        {"id": "3", "content": "人为什么做梦？", "label": "生物"},

        # 组2：大象鼻子相关（应该聚在一起）
        {"id": "4", "content": "大象的鼻子为什么长？", "label": "生物"},
        {"id": "5", "content": "为什么大象的鼻子长？", "label": "生物"},
        {"id": "6", "content": "大象鼻子为什么这么长？", "label": "生物"},

        # 组3：天空蓝色相关（应该聚在一起）
        {"id": "7", "content": "天空为什么是蓝色的？", "label": "生物"},
        {"id": "8", "content": "为什么天空是蓝色的？", "label": "生物"},
        {"id": "9", "content": "天空为什么蓝？", "label": "生物"},

        # 组4：天空红色相关（应该与蓝色分开）
        {"id": "17", "content": "天空为什么是红色的？", "label": "生物"},
        {"id": "18", "content": "为什么天空是红色的？", "label": "生物"},

        # 组5：天空颜色询问（应该与具体颜色分开）
        {"id": "16", "content": "天空是什么颜色的？", "label": "生物"},
        {"id": "19", "content": "天空什么颜色？", "label": "生物"},

        # 组6：萤火虫发光相关（应该聚在一起）
        {"id": "10", "content": "萤火虫为什么会发光？", "label": "生物"},
        {"id": "11", "content": "为什么萤火虫会发光？", "label": "生物"},
        {"id": "12", "content": "萤火虫为什么发光？", "label": "生物"},

        # 组7：螃蟹变色相关（应该聚在一起）
        {"id": "13", "content": "螃蟹煮熟了为什么会变色？", "label": "生物"},
        {"id": "14", "content": "为什么螃蟹煮熟了会变色？", "label": "生物"},
        {"id": "15", "content": "煮熟的螃蟹为什么变色？", "label": "生物"},

        # 组8：测试边界情况
        {"id": "20", "content": "老虎为什么有条纹？", "label": "生物"},
        {"id": "21", "content": "斑马为什么有条纹？", "label": "生物"},  # 应该与老虎分开

        # 组9：实际聚类错误案例测试（这些应该完全分开）
        {"id": "22", "content": "为什么萤火虫会发光？", "label": "生物"},  # 发光现象
        {"id": "23", "content": "为什么人会死？", "label": "生物"},        # 死亡现象
        {"id": "24", "content": "龟壳为什么那么硬？", "label": "生物"},    # 硬度现象
        {"id": "25", "content": "人为什么会做梦呢？", "label": "生物"},    # 做梦现象

        # 组10：更多不同语义的问题（应该各自独立）
        {"id": "26", "content": "鱼为什么能在水中呼吸？", "label": "生物"},  # 呼吸现象
        {"id": "27", "content": "鸟为什么会飞？", "label": "生物"},         # 飞行现象
        {"id": "28", "content": "蛇为什么没有腿？", "label": "生物"},       # 身体结构
        {"id": "29", "content": "花为什么有香味？", "label": "生物"},       # 气味现象

        # 组11：跨领域测试（验证算法通用性）
        {"id": "30", "content": "汽车为什么会跑？", "label": "技术"},       # 交通工具
        {"id": "31", "content": "电脑为什么会发热？", "label": "技术"},     # 电子设备
        {"id": "32", "content": "太阳为什么会发光？", "label": "天文"},     # 天体现象
    ]

    return test_data


def test_config_system():
    """测试配置系统"""
    print("=== 测试配置系统 ===")

    # 测试默认配置
    default_config = SemanticMatchingConfig()
    print(f"默认配置:")
    print(f"  相似度阈值: {default_config.similarity_threshold}")
    print(f"  权重阈值: {default_config.weight_threshold}")
    print(f"  结构阈值: {default_config.structure_threshold}")

    # 测试超严格配置
    strict_config = default_config.get_strict_config()
    print(f"\n超严格配置:")
    print(f"  相似度阈值: {strict_config.similarity_threshold}")
    print(f"  权重阈值: {strict_config.weight_threshold}")
    print(f"  结构阈值: {strict_config.structure_threshold}")

    # 测试中等配置
    moderate_config = default_config.get_moderate_config()
    print(f"\n中等配置:")
    print(f"  相似度阈值: {moderate_config.similarity_threshold}")
    print(f"  权重阈值: {moderate_config.weight_threshold}")
    print(f"  结构阈值: {moderate_config.structure_threshold}")

def test_semantic_signature():
    """测试通用语义签名功能"""
    print("\n=== 测试通用语义签名提取功能 ===")

    analyzer = BiologyQAClusterAnalyzer("dummy_path")

    # 测试语义签名提取
    test_questions = [
        "天空为什么是蓝色的？",
        "天空为什么是红色的？",
        "天空是什么颜色的？",
        "老虎为什么有条纹？",
        "斑马为什么有条纹？",
        "为什么人会死？",
        "人为什么会做梦？",
        "汽车为什么会跑？",  # 非生物领域测试
        "电脑为什么会发热？"  # 非生物领域测试
    ]

    print("通用语义签名提取结果:")
    for i, question in enumerate(test_questions, 1):
        processed = analyzer.preprocess_text(question)
        content_words, weights = analyzer._extract_semantic_signature(processed)

        print(f"\n{i}. {question}")
        print(f"   预处理: {processed}")
        print(f"   内容词: {content_words}")
        print(f"   权重: {weights}")

        # 显示高权重词汇
        high_weight_words = {w for w, weight in weights.items() if weight >= 0.7}
        if high_weight_words:
            print(f"   高权重词: {high_weight_words}")

def test_universal_matching():
    """测试通用语义匹配功能"""
    print("\n=== 测试通用语义匹配功能 ===")

    # 测试不同配置下的效果
    configs = {
        "默认配置": SemanticMatchingConfig(),
        "超严格配置": SemanticMatchingConfig().get_strict_config(),
        "中等配置": SemanticMatchingConfig().get_moderate_config()
    }

    # 关键测试用例（包含不同领域）
    critical_test_cases = [
        # 天空颜色细分测试
        ("天空为什么是蓝色的？", "为什么天空是蓝色的？", "应该聚在一起：相同蓝色概念"),
        ("天空为什么是蓝色的？", "天空为什么蓝？", "应该聚在一起：相同蓝色概念"),
        ("天空为什么是蓝色的？", "天空为什么是红色的？", "应该分开：蓝色vs红色"),
        ("天空为什么是蓝色的？", "天空是什么颜色的？", "应该分开：具体颜色vs询问颜色"),
        ("天空是什么颜色的？", "天空什么颜色？", "应该聚在一起：相同询问颜色"),

        # 不同实体相同现象测试
        ("老虎为什么有条纹？", "斑马为什么有条纹？", "应该分开：不同动物"),
        ("为什么萤火虫会发光？", "为什么人会死？", "应该分开：发光vs死亡"),
        ("龟壳为什么那么硬？", "蛇为什么没有腿？", "应该分开：硬度vs身体结构"),

        # 相同语义不同表述测试
        ("人为什么会做梦？", "为什么人会做梦？", "应该聚在一起：相同做梦概念"),
        ("人为什么会做梦？", "人为什么做梦呢？", "应该聚在一起：相同做梦概念"),

        # 跨领域测试（验证通用性）
        ("汽车为什么会跑？", "汽车为什么能跑？", "应该聚在一起：相同概念"),
        ("汽车为什么会跑？", "飞机为什么会飞？", "应该分开：不同交通工具"),
        ("电脑为什么会发热？", "手机为什么会发热？", "应该分开：不同设备")
    ]

    for config_name, config in configs.items():
        print(f"\n--- 使用{config_name}测试 ---")
        analyzer = BiologyQAClusterAnalyzer("dummy_path", config)

        correct_count = 0
        total_count = len(critical_test_cases)

        for i, (text1, text2, expected) in enumerate(critical_test_cases, 1):
            processed1 = analyzer.preprocess_text(text1)
            processed2 = analyzer.preprocess_text(text2)

            # 计算相似度
            similarity = analyzer._compute_semantic_similarity(processed1, processed2)

            # 判断结果
            if "应该聚在一起" in expected:
                is_correct = similarity >= 1.0
            else:  # 应该分开
                is_correct = similarity == 0.0

            if is_correct:
                correct_count += 1

            # 只显示错误的案例
            if not is_correct:
                print(f"   ❌ 案例{i}: {expected}")
                print(f"      '{text1}' vs '{text2}' -> 相似度: {similarity:.1f}")

        accuracy = correct_count / total_count
        print(f"   准确率: {correct_count}/{total_count} = {accuracy:.1%}")

        if accuracy >= 0.9:
            print(f"   🎉 {config_name}表现优秀！")
        elif accuracy >= 0.7:
            print(f"   👍 {config_name}表现良好")
        else:
            print(f"   ⚠️ {config_name}需要改进")

    print("\n通用语义匹配功能测试完成！")

def test_problematic_cases():
    """专门测试实际聚类中的错误案例"""
    print("\n=== 测试实际聚类错误案例 ===")

    analyzer = BiologyQAClusterAnalyzer("dummy_path")

    # 实际出现聚类错误的问题
    problematic_questions = [
        "为什么萤火虫会发光？",      # processed: "萤火虫 发光"
        "为什么人会死？",           # processed: "人会"
        "龟壳为什么那么硬？",       # processed: "龟壳"
        "人为什么会做梦呢？"        # processed: "做梦"
    ]

    print("问题预处理结果:")
    processed_texts = []
    core_words_list = []

    for i, question in enumerate(problematic_questions, 1):
        processed = analyzer.preprocess_text(question)
        core_words = analyzer._extract_core_words(processed)
        processed_texts.append(processed)
        core_words_list.append(core_words)

        print(f"{i}. 原文: {question}")
        print(f"   处理后: '{processed}'")
        print(f"   核心词汇: {core_words}")
        print()

    print("两两相似度计算:")
    similarities = []
    for i in range(len(processed_texts)):
        for j in range(i + 1, len(processed_texts)):
            sim = analyzer._compute_precise_similarity(processed_texts[i], processed_texts[j])
            similarities.append(sim)

            q1 = problematic_questions[i]
            q2 = problematic_questions[j]

            print(f"问题{i+1} vs 问题{j+1}: 相似度 = {sim:.3f}")
            print(f"  '{q1}' vs '{q2}'")

            if sim >= 0.8:
                print(f"  ❌ 错误：这两个问题会被聚在一起！")
            else:
                print(f"  ✅ 正确：这两个问题会被分开")
            print()

    # 统计结果
    high_sim_count = sum(1 for sim in similarities if sim >= 0.8)
    total_pairs = len(similarities)

    print(f"=== 错误聚类风险评估 ===")
    print(f"总问题对数: {total_pairs}")
    print(f"高相似度对数 (≥0.8): {high_sim_count}")
    print(f"错误聚类风险: {high_sim_count}/{total_pairs} = {high_sim_count/total_pairs:.1%}")

    if high_sim_count == 0:
        print("🎉 优秀！所有不同语义的问题都会被正确分开")
    elif high_sim_count <= total_pairs * 0.2:
        print("👍 良好！大部分不同语义的问题会被正确分开")
    else:
        print("⚠️ 需要改进！仍有较多不同语义的问题会被错误聚类")

    return high_sim_count == 0


def test_clustering():
    """测试通用语义匹配K-means聚类效果"""
    print("\n=== 测试通用语义匹配K-means聚类效果 ===")

    # 创建测试数据文件
    test_data = create_test_data()
    test_file = "/tmp/test_universal_semantic_kmeans.json"

    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)

    # 使用超严格配置创建分析器
    strict_config = SemanticMatchingConfig().get_strict_config()
    analyzer = BiologyQAClusterAnalyzer(test_file, strict_config)

    # 加载数据
    analyzer.load_data()

    # 准备特征
    analyzer.prepare_features()

    # 显示预处理和语义签名提取结果
    print("\n预处理和语义签名提取结果:")
    for i, (original, processed) in enumerate(zip(
            [item['content'] for item in analyzer.data],
            analyzer.processed_texts
    )):
        content_words, weights = analyzer._extract_semantic_signature(processed)
        high_weight_words = {w for w, weight in weights.items() if weight >= 0.7}

        print(f"{i + 1:2d}. 原文: {original}")
        print(f"    处理后: {processed}")
        print(f"    内容词: {content_words}")
        print(f"    高权重词: {high_weight_words}")
        print()

    # 显示精确匹配矩阵信息
    print("精确匹配分析:")
    similarity_matrix = 1 - analyzer.precise_distance_matrix
    high_sim_pairs = np.sum(similarity_matrix >= 0.8) - len(similarity_matrix)
    print(f"高相似度对数量 (≥0.8): {high_sim_pairs // 2}")

    # 显示TF-IDF矩阵信息
    print(f"\nTF-IDF矩阵形状: {analyzer.tfidf_matrix.shape}")
    print(f"TF-IDF矩阵稀疏度: {(1 - analyzer.tfidf_matrix.nnz / (analyzer.tfidf_matrix.shape[0] * analyzer.tfidf_matrix.shape[1])) * 100:.2f}%")

    # 显示特征词汇（兼容不同版本的scikit-learn）
    try:
        feature_names = analyzer.vectorizer.get_feature_names_out()
    except AttributeError:
        feature_names = analyzer.vectorizer.get_feature_names()

    print(f"特征词汇数量: {len(feature_names)}")
    print(f"前10个特征词: {list(feature_names[:10])}")

    # 寻找最优聚类数并聚类（语义纯度优先，支持更多聚类）
    analyzer.find_optimal_k(max_k=len(analyzer.data))  # 允许每个问题独立聚类
    analyzer.perform_clustering()

    # 显示聚类结果并验证精确匹配效果
    print("\n=== 聚类结果验证 ===")

    # 按聚类分组显示
    cluster_groups = {}
    for i, (original, cluster_id) in enumerate(zip(
            [item['content'] for item in analyzer.data],
            analyzer.cluster_labels
    )):
        if cluster_id not in cluster_groups:
            cluster_groups[cluster_id] = []
        cluster_groups[cluster_id].append((i+1, original))

    for cluster_id in sorted(cluster_groups.keys()):
        questions = cluster_groups[cluster_id]
        print(f"\n聚类 {cluster_id} ({len(questions)}个问题):")
        for idx, question in questions:
            print(f"  {idx:2d}. {question}")

        # 验证聚类内部相似度
        if len(questions) > 1:
            indices = [q[0]-1 for q in questions]
            similarities = []
            for i in range(len(indices)):
                for j in range(i+1, len(indices)):
                    sim = similarity_matrix[indices[i], indices[j]]
                    similarities.append(sim)

            if similarities:
                avg_sim = np.mean(similarities)
                min_sim = np.min(similarities)
                quality = "优秀" if avg_sim >= 0.8 else "良好" if avg_sim >= 0.6 else "一般"
                print(f"    内部相似度: 平均={avg_sim:.3f}, 最低={min_sim:.3f}, 质量={quality}")

    # 验证预期的聚类效果
    print("\n=== 预期效果验证 ===")
    expected_groups = [
        ([1, 2, 3], "做梦相关"),
        ([4, 5, 6], "大象鼻子相关"),
        ([7, 8, 9], "天空蓝色相关"),
        ([10, 11], "天空红色相关"),
        ([12, 13], "天空颜色询问"),
        ([14, 15, 16], "萤火虫发光相关"),
        ([17, 18, 19], "螃蟹变色相关"),
        ([20], "老虎条纹"),
        ([21], "斑马条纹"),

        # 实际聚类错误案例（这些应该各自独立）
        ([22], "萤火虫发光（重复）"),  # 与14-16不同的是这个单独
        ([23], "人类死亡"),
        ([24], "龟壳硬度"),
        ([25], "做梦现象（重复）"),   # 与1-3不同的是这个单独
        ([26], "鱼类呼吸"),
        ([27], "鸟类飞行"),
        ([28], "蛇类身体结构"),
        ([29], "花朵气味")
    ]

    correct_groups = 0
    total_groups = len(expected_groups)

    for expected_indices, group_name in expected_groups:
        # 检查这些问题是否被聚在同一类
        if len(expected_indices) == 1:
            print(f"✓ {group_name}: 单独问题，无需验证")
            correct_groups += 1
        else:
            cluster_ids = [analyzer.cluster_labels[i-1] for i in expected_indices]
            if len(set(cluster_ids)) == 1:
                print(f"✅ {group_name}: 正确聚在聚类{cluster_ids[0]}")
                correct_groups += 1
            else:
                print(f"❌ {group_name}: 被分散到多个聚类{set(cluster_ids)}")

    accuracy = correct_groups / total_groups
    print(f"\n聚类准确率: {correct_groups}/{total_groups} = {accuracy:.1%}")

    if accuracy >= 0.8:
        print("🎉 精确匹配聚类效果优秀！")
    elif accuracy >= 0.6:
        print("👍 精确匹配聚类效果良好")
    else:
        print("⚠️ 精确匹配聚类效果需要改进")

    print("\n=== 精确匹配K-means聚类测试完成 ===")

    # 分析聚类特征
    print("\n开始聚类特征分析...")
    cluster_analysis = analyzer.analyze_clusters()

    # 保存结果
    print("\n保存聚类结果...")
    analyzer.save_results(cluster_analysis)

    # 生成报告
    print("\n生成聚类分析报告...")
    analyzer.generate_report(cluster_analysis)

    print("\n=== K-means聚类测试完成 ===")


if __name__ == "__main__":
    print("🔍 开始超严格语义匹配聚类算法测试")
    print("="*60)

    # 测试1：语义签名提取
    test_semantic_signature()

    # 测试2：超严格语义匹配
    semantic_test_passed = test_precise_matching()

    # 测试3：实际错误案例
    problematic_test_passed = test_problematic_cases()

    # 测试4：完整聚类测试
    test_clustering()

    # 总结
    print("\n" + "="*60)
    print("🏁 测试总结")
    print("="*60)

    if semantic_test_passed and problematic_test_passed:
        print("🎉 所有测试通过：超严格语义匹配算法工作正常")
        print("✅ 能正确区分：蓝色天空 vs 红色天空 vs 询问颜色")
        print("✅ 能正确区分：不同动物的相同现象")
        print("✅ 能正确聚类：相同语义的不同表述")
    elif semantic_test_passed:
        print("👍 语义匹配测试通过，但实际案例仍有问题")
        print("💡 建议：检查实际数据的预处理效果")
    else:
        print("❌ 语义匹配算法仍有问题，需要进一步调整")
        print("💡 建议：检查语义签名提取和相似度计算逻辑")

    print("\n完整测试报告已生成，请查看聚类结果和准确率统计。")
