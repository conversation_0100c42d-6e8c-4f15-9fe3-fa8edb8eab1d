#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按label字段对JSON数据进行分类的脚本
将输入文件中的数据按照label值分组，为每个label创建单独的JSON文件
"""

import json
import os
import sys
import time
from collections import defaultdict
from pathlib import Path


def load_data(input_file_path):
    """
    加载输入JSON文件
    """
    print(f"正在加载数据文件: {input_file_path}")
    
    try:
        with open(input_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"成功加载 {len(data)} 条记录")
        return data
    
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误: JSON文件格式错误 - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 加载文件失败 - {e}")
        sys.exit(1)


def classify_by_label(data):
    """
    按label字段对数据进行分类
    """
    print("正在按label字段分类数据...")
    
    classified_data = defaultdict(list)
    
    for record in data:
        if 'label' not in record:
            print(f"警告: 记录缺少label字段，跳过: {record}")
            continue
        
        label = record['label']
        classified_data[label].append(record)
    
    print(f"分类完成，共发现 {len(classified_data)} 个不同的label")
    
    return classified_data


def create_output_directory(output_dir):
    """
    创建输出目录
    """
    try:
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        print(f"输出目录已准备: {output_dir}")
    except Exception as e:
        print(f"错误: 无法创建输出目录 {output_dir} - {e}")
        sys.exit(1)


def sanitize_filename(label):
    """
    清理label字符串，使其适合作为文件名
    """
    # 替换不适合文件名的字符
    invalid_chars = '<>:"/\\|?*'
    sanitized = label
    
    for char in invalid_chars:
        sanitized = sanitized.replace(char, '_')
    
    # 去除首尾空格并限制长度
    sanitized = sanitized.strip()[:100]
    
    return sanitized


def save_classified_data(classified_data, output_dir):
    """
    将分类后的数据保存到单独的JSON文件
    """
    print("正在保存分类后的数据...")
    
    statistics = {}
    
    for label, records in classified_data.items():
        # 清理文件名
        safe_filename = sanitize_filename(label)
        output_file = os.path.join(output_dir, f"{safe_filename}.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
            
            statistics[label] = len(records)
            print(f"已保存 {len(records)} 条记录到: {output_file}")
            
        except Exception as e:
            print(f"错误: 保存文件失败 {output_file} - {e}")
            continue
    
    return statistics


def generate_summary_report(statistics, output_dir, total_records, processing_time):
    """
    生成处理统计报告
    """
    report_file = os.path.join(output_dir, "classification_summary.txt")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=== 数据分类处理报告 ===\n\n")
            f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总处理时间: {processing_time:.2f} 秒\n\n")
            
            f.write("=== 统计信息 ===\n")
            f.write(f"总记录数: {total_records:,}\n")
            f.write(f"分类数量: {len(statistics)}\n\n")
            
            f.write("=== 各类别详情 ===\n")
            # 按记录数量降序排列
            sorted_stats = sorted(statistics.items(), key=lambda x: x[1], reverse=True)
            
            for label, count in sorted_stats:
                percentage = (count / total_records) * 100
                f.write(f"{label}: {count:,} 条记录 ({percentage:.2f}%)\n")
        
        print(f"统计报告已保存: {report_file}")
        
    except Exception as e:
        print(f"警告: 无法生成统计报告 - {e}")


def print_summary(statistics, total_records, processing_time):
    """
    打印处理摘要
    """
    print("\n" + "="*50)
    print("处理完成摘要")
    print("="*50)
    print(f"总记录数: {total_records:,}")
    print(f"分类数量: {len(statistics)}")
    print(f"处理时间: {processing_time:.2f} 秒")
    print(f"平均处理速度: {total_records/processing_time:.0f} 条/秒")
    
    print("\n前10个最大类别:")
    sorted_stats = sorted(statistics.items(), key=lambda x: x[1], reverse=True)
    for i, (label, count) in enumerate(sorted_stats[:10], 1):
        percentage = (count / total_records) * 100
        print(f"{i:2d}. {label}: {count:,} 条 ({percentage:.2f}%)")


def main():
    """
    主函数
    """
    # 配置文件路径
    input_file = r"/work1/data/fanzhang39/share/kexue/wjp/Type13/deduplication/data/1.5_pro_13_cls.json"
    output_dir = r"/work1/data/fanzhang39/share/kexue/wjp/Type13/deduplication/data/classified"
    
    print("开始按label分类处理...")
    start_time = time.time()
    
    # 1. 加载数据
    data = load_data(input_file)
    total_records = len(data)
    
    # 2. 按label分类
    classified_data = classify_by_label(data)
    
    # 3. 创建输出目录
    create_output_directory(output_dir)
    
    # 4. 保存分类数据
    statistics = save_classified_data(classified_data, output_dir)
    
    # 5. 计算处理时间
    processing_time = time.time() - start_time
    
    # 6. 生成报告
    generate_summary_report(statistics, output_dir, total_records, processing_time)
    
    # 7. 打印摘要
    print_summary(statistics, total_records, processing_time)
    
    print(f"\n所有文件已保存到: {output_dir}")
    print("分类处理完成！")


if __name__ == "__main__":
    main()
