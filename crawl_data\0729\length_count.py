"""
统计merged_sci_data.json中answer字段各子字段的长度，并生成柱状图
"""

import json
import os
import matplotlib.pyplot as plt
import numpy as np
from collections import Counter

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号


def count_answer_lengths(data):
    """统计每条记录的answer子字段长度"""
    length_stats = []

    for item in data:
        item_id = item.get('id', '')
        answer = item.get('answer', {})

        # 统计各子字段长度
        answer_length = {
            "居里夫人_小行动": len(answer.get("居里夫人_小行动", "")),
            "居里夫人_冷知识": len(answer.get("居里夫人_冷知识", "")),
            "达尔文_小行动": len(answer.get("达尔文_小行动", "")),
            "达尔文_冷知识": len(answer.get("达尔文_冷知识", ""))
        }

        length_stats.append({
            "id": item_id,
            "answer_length": answer_length
        })

    return length_stats


def analyze_length_distribution(length_stats):
    """分析长度分布"""
    # 收集所有字段的长度数据
    field_lengths = {
        "居里夫人_小行动": [],
        "居里夫人_冷知识": [],
        "达尔文_小行动": [],
        "达尔文_冷知识": []
    }

    for item in length_stats:
        answer_length = item["answer_length"]
        for field, length in answer_length.items():
            field_lengths[field].append(length)

    return field_lengths


def create_length_distribution_charts(field_lengths, output_dir):
    """创建长度分布柱状图"""
    # 创建2x2的子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Answer字段长度分布统计', fontsize=16, fontweight='bold')

    fields = ["居里夫人_小行动", "居里夫人_冷知识", "达尔文_小行动", "达尔文_冷知识"]
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

    for i, field in enumerate(fields):
        row = i // 2
        col = i % 2
        ax = axes[row, col]

        lengths = field_lengths[field]

        # 统计长度分布
        length_counter = Counter(lengths)

        # 按长度排序
        sorted_lengths = sorted(length_counter.items())

        if sorted_lengths:
            x_values = [x[0] for x in sorted_lengths]
            y_values = [x[1] for x in sorted_lengths]

            # 创建柱状图
            bars = ax.bar(x_values, y_values, color=colors[i], alpha=0.7, edgecolor='black', linewidth=0.5)

            # 在柱子上显示数值
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{int(height)}',
                           ha='center', va='bottom', fontsize=8)

            # 设置标题和标签
            ax.set_title(f'{field}\n(总计: {len(lengths)}条, 非空: {sum(1 for x in lengths if x > 0)}条)',
                        fontsize=12, fontweight='bold')
            ax.set_xlabel('字符长度', fontsize=10)
            ax.set_ylabel('数量', fontsize=10)

            # 设置网格
            ax.grid(True, alpha=0.3)

            # 统计信息
            non_empty = [x for x in lengths if x > 0]
            if non_empty:
                avg_length = np.mean(non_empty)
                max_length = max(non_empty)
                min_length = min(non_empty)

                # 在图上添加统计信息
                stats_text = f'平均: {avg_length:.1f}\n最大: {max_length}\n最小: {min_length}'
                ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                       verticalalignment='top', fontsize=9,
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        else:
            ax.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(field, fontsize=12, fontweight='bold')

    plt.tight_layout()

    # 保存图片
    chart_path = os.path.join(output_dir, 'answer_length_distribution.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"柱状图已保存到: {chart_path}")

    plt.show()


def create_summary_chart(field_lengths, output_dir):
    """创建总体统计柱状图"""
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Answer字段统计总览', fontsize=16, fontweight='bold')

    fields = ["居里夫人_小行动", "居里夫人_冷知识", "达尔文_小行动", "达尔文_冷知识"]
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

    # 统计各种数据
    total_counts = []
    non_empty_counts = []
    within_200_counts = []
    above_200_counts = []
    avg_lengths = []

    for field in fields:
        lengths = field_lengths[field]
        non_empty = [x for x in lengths if x > 0]
        within_200 = [x for x in non_empty if x <= 200]
        above_200 = [x for x in non_empty if x > 200]

        total_counts.append(len(lengths))
        non_empty_counts.append(len(non_empty))
        within_200_counts.append(len(within_200))
        above_200_counts.append(len(above_200))
        avg_lengths.append(np.mean(non_empty) if non_empty else 0)

    # 第一个图：总数量 vs 非空数量
    ax1 = axes[0, 0]
    x_pos = np.arange(len(fields))
    width = 0.35

    bars1 = ax1.bar(x_pos - width/2, total_counts, width, label='总数量', color=colors, alpha=0.7)
    bars2 = ax1.bar(x_pos + width/2, non_empty_counts, width, label='非空数量', color=colors, alpha=1.0)

    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height)}',
                    ha='center', va='bottom', fontsize=9)

    ax1.set_title('总数量 vs 非空数量', fontsize=12, fontweight='bold')
    ax1.set_ylabel('数量', fontsize=10)
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels([f.replace('_', '\n') for f in fields], fontsize=9)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 第二个图：200字以内 vs 200字以上
    ax2 = axes[0, 1]
    bars3 = ax2.bar(x_pos - width/2, within_200_counts, width, label='≤200字', color='#2ECC71', alpha=0.8)
    bars4 = ax2.bar(x_pos + width/2, above_200_counts, width, label='>200字', color='#E74C3C', alpha=0.8)

    # 添加数值标签
    for bars in [bars3, bars4]:
        for bar in bars:
            height = bar.get_height()
            if height > 0:
                ax2.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height)}',
                        ha='center', va='bottom', fontsize=9)

    ax2.set_title('字数分布：200字分界线', fontsize=12, fontweight='bold')
    ax2.set_ylabel('数量', fontsize=10)
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels([f.replace('_', '\n') for f in fields], fontsize=9)
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 第三个图：平均长度
    ax3 = axes[1, 0]
    bars5 = ax3.bar(fields, avg_lengths, color=colors, alpha=0.7)

    # 添加数值标签
    for bar in bars5:
        height = bar.get_height()
        if height > 0:
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.1f}',
                    ha='center', va='bottom', fontsize=9)

    ax3.set_title('各字段平均长度', fontsize=12, fontweight='bold')
    ax3.set_ylabel('平均字符长度', fontsize=10)
    ax3.set_xticklabels([f.replace('_', '\n') for f in fields], fontsize=9)
    ax3.grid(True, alpha=0.3)

    # 第四个图：比例饼图（200字分界）
    ax4 = axes[1, 1]
    total_within_200 = sum(within_200_counts)
    total_above_200 = sum(above_200_counts)

    if total_within_200 + total_above_200 > 0:
        sizes = [total_within_200, total_above_200]
        labels = [f'≤200字\n({total_within_200}条)', f'>200字\n({total_above_200}条)']
        colors_pie = ['#2ECC71', '#E74C3C']

        wedges, texts, autotexts = ax4.pie(sizes, labels=labels, colors=colors_pie, autopct='%1.1f%%',
                                          startangle=90, textprops={'fontsize': 10})
        ax4.set_title('全体字数分布比例', fontsize=12, fontweight='bold')
    else:
        ax4.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax4.transAxes)
        ax4.set_title('全体字数分布比例', fontsize=12, fontweight='bold')

    plt.tight_layout()

    # 保存图片
    summary_path = os.path.join(output_dir, 'answer_summary_stats.png')
    plt.savefig(summary_path, dpi=300, bbox_inches='tight')
    print(f"总体统计图已保存到: {summary_path}")

    plt.show()


def print_summary_stats(field_lengths):
    """打印统计摘要"""
    print("\n=== Answer字段长度统计摘要 ===")

    for field, lengths in field_lengths.items():
        non_empty = [x for x in lengths if x > 0]
        empty_count = len(lengths) - len(non_empty)

        # 按长度分类统计
        within_200 = [x for x in non_empty if x <= 200]
        above_200 = [x for x in non_empty if x > 200]

        print(f"\n{field}:")
        print(f"  总数量: {len(lengths)}")
        print(f"  非空数量: {len(non_empty)}")
        print(f"  空值数量: {empty_count}")
        print(f"  200字以内数量: {len(within_200)}")
        print(f"  200字以上数量: {len(above_200)}")

        if non_empty:
            print(f"  平均长度: {np.mean(non_empty):.1f}")
            print(f"  最大长度: {max(non_empty)}")
            print(f"  最小长度: {min(non_empty)}")
            print(f"  中位数长度: {np.median(non_empty):.1f}")

            # 计算比例
            if len(non_empty) > 0:
                within_200_pct = len(within_200) / len(non_empty) * 100
                above_200_pct = len(above_200) / len(non_empty) * 100
                print(f"  200字以内占比: {within_200_pct:.1f}%")
                print(f"  200字以上占比: {above_200_pct:.1f}%")


def main():
    """主函数"""
    # 定义文件路径
    base_dir = r"D:\ProJects\kexue\crawl_data\0729\03"
    input_file = os.path.join(base_dir, "sci_answer.json")
    output_file = os.path.join(base_dir, "answer_length_stats.json")

    print(f"正在加载数据: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件不存在 {input_file}")
        return

    # 加载数据
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载 {len(data)} 条数据")
    except Exception as e:
        print(f"加载数据失败: {e}")
        return

    # 统计长度
    length_stats = count_answer_lengths(data)

    # 分析长度分布
    field_lengths = analyze_length_distribution(length_stats)

    # 打印统计摘要
    print_summary_stats(field_lengths)

    # 创建图表
    print("\n正在生成图表...")
    create_length_distribution_charts(field_lengths, base_dir)
    create_summary_chart(field_lengths, base_dir)

    # 保存详细统计结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(length_stats, f, ensure_ascii=False, indent=2)

    print(f"\n详细统计数据已保存到: {output_file}")
    print("分析完成！")


if __name__ == "__main__":
    main()
