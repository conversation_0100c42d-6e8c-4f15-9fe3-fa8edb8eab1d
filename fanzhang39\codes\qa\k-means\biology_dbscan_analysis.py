#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生物类问答DBSCAN聚类分析（优化内存版本）
基于词汇集合+Jaccard相似度+DBSCAN聚类算法
使用分批处理和稀疏矩阵优化内存使用
"""

import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.manifold import MDS
import jieba
import jieba.posseg as pseg
from collections import Counter
import re
import warnings
from scipy.sparse import csr_matrix, lil_matrix
import gc
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class BiologyQADBSCANAnalyzer:
    """生物类问答DBSCAN聚类分析器（内存优化版）"""
    
    def __init__(self, data_path):
        """
        初始化分析器
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.data = None
        self.processed_texts = None
        self.word_sets = None  # 存储每个问题的词汇集合
        self.similarity_matrix = None  # 存储相似度矩阵（稀疏）
        self.distance_matrix = None  # 存储距离矩阵
        self.optimal_eps = None  # DBSCAN的最优eps参数
        self.clustering_model = None  # 聚类模型
        self.cluster_labels = None
        
        # 聚类参数设置
        self.jaccard_threshold = 0.8  # Jaccard相似度阈值
        self.min_samples = 1  # DBSCAN最小样本数（设为1避免噪声点）
        self.batch_size = 1000  # 分批处理大小
        self.sparse_threshold = 0.1  # 稀疏矩阵阈值
        
        # 加载哈工大停用词表
        self.stop_words = self._load_stopwords()
    
    def _load_stopwords(self):
        """
        加载哈工大停用词表
        
        Returns:
            set: 停用词集合
        """
        stopwords_file = "hagongda_stopwords.txt"
        stop_words = set()
        
        try:
            with open(stopwords_file, 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip()
                    if word:  # 过滤空行
                        stop_words.add(word)
            print(f"成功加载 {len(stop_words)} 个停用词")
        except FileNotFoundError:
            print(f"警告: 停用词文件 {stopwords_file} 未找到，使用默认停用词")
            # 如果文件不存在，使用原来的默认停用词
            stop_words = {
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
                '自己', '这', '那', '什么', '为什么', '怎么', '如何', '吗', '呢', '啊', '呀'
            }
        except Exception as e:
            print(f"加载停用词文件时出错: {e}，使用默认停用词")
            stop_words = {
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
                '自己', '这', '那', '什么', '为什么', '怎么', '如何', '吗', '呢', '啊', '呀'
            }
        
        return stop_words

    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 条生物类问答数据")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def preprocess_text(self, text):
        """
        简化的文本预处理方法
        
        Args:
            text: 原始文本
            
        Returns:
            处理后的文本
        """
        if not text or not isinstance(text, str):
            return ""
        
        # 使用jieba进行基础分词和词性标注
        words = []
        for word, flag in pseg.cut(text):
            # 基本过滤条件
            if (len(word) > 1 and 
                word not in self.stop_words and 
                flag in ['n', 'v', 'a', 'nr', 'ns', 'nt', 'nz', 'vn', 'an']):
                # 过滤纯数字和标点
                if not re.match(r'^[\d\W]+$', word):
                    words.append(word)
        
        return ' '.join(words)
    
    def extract_word_sets(self):
        """
        将预处理后的文本转换为词汇集合
        
        Returns:
            list: 每个问题对应的词汇集合列表
        """
        word_sets = []
        for text in self.processed_texts:
            if text.strip():
                # 将文本分割为词汇集合
                words = set(text.split())
                word_sets.append(words)
            else:
                word_sets.append(set())
        
        return word_sets
    
    def jaccard_similarity(self, set1, set2):
        """
        计算两个词汇集合的Jaccard相似度
        
        Args:
            set1: 第一个词汇集合
            set2: 第二个词汇集合
            
        Returns:
            float: Jaccard相似度 (0-1之间)
        """
        if len(set1) == 0 and len(set2) == 0:
            return 1.0
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        if union == 0:
            return 0.0
        
        return intersection / union

    def compute_sparse_similarity_matrix(self):
        """
        计算稀疏相似度矩阵（内存优化版本）
        只存储相似度高于阈值的值

        Returns:
            scipy.sparse.csr_matrix: 稀疏相似度矩阵
        """
        n = len(self.word_sets)
        print(f"计算稀疏相似度矩阵 ({n}x{n})...")
        print(f"使用阈值: {self.sparse_threshold}")

        # 使用lil_matrix进行构建，然后转换为csr_matrix
        similarity_matrix = lil_matrix((n, n), dtype=np.float32)

        total_pairs = n * (n - 1) // 2
        processed_pairs = 0
        stored_pairs = 0

        for i in range(n):
            # 对角线元素
            similarity_matrix[i, i] = 1.0

            for j in range(i + 1, n):
                sim = self.jaccard_similarity(self.word_sets[i], self.word_sets[j])

                # 只存储高于阈值的相似度
                if sim >= self.sparse_threshold:
                    similarity_matrix[i, j] = sim
                    similarity_matrix[j, i] = sim
                    stored_pairs += 1

                processed_pairs += 1

                # 显示进度
                if processed_pairs % 10000 == 0:
                    progress = processed_pairs / total_pairs * 100
                    sparsity = stored_pairs / processed_pairs * 100 if processed_pairs > 0 else 0
                    print(f"进度: {progress:.1f}% | 存储的相似对: {stored_pairs} ({sparsity:.2f}%)")

                    # 强制垃圾回收
                    if processed_pairs % 50000 == 0:
                        gc.collect()

        # 转换为csr_matrix以提高访问效率
        similarity_matrix = similarity_matrix.tocsr()

        print(f"稀疏矩阵构建完成:")
        print(f"- 总元素数: {n * n:,}")
        print(f"- 存储元素数: {similarity_matrix.nnz:,}")
        print(f"- 稀疏度: {(1 - similarity_matrix.nnz / (n * n)) * 100:.2f}%")
        print(f"- 内存使用: {similarity_matrix.data.nbytes / 1024 / 1024:.1f} MB")

        return similarity_matrix

    def compute_batch_similarity_matrix(self):
        """
        分批计算相似度矩阵（内存优化版本）

        Returns:
            numpy.ndarray: 相似度矩阵
        """
        n = len(self.word_sets)
        print(f"分批计算相似度矩阵 ({n}x{n})...")
        print(f"批次大小: {self.batch_size}")

        # 使用float32减少内存使用
        similarity_matrix = np.zeros((n, n), dtype=np.float32)

        num_batches = (n + self.batch_size - 1) // self.batch_size

        for i_batch in range(num_batches):
            i_start = i_batch * self.batch_size
            i_end = min((i_batch + 1) * self.batch_size, n)

            for j_batch in range(i_batch, num_batches):
                j_start = j_batch * self.batch_size
                j_end = min((j_batch + 1) * self.batch_size, n)

                print(f"处理批次 ({i_batch+1},{j_batch+1}) / ({num_batches},{num_batches})")

                # 计算当前批次的相似度
                for i in range(i_start, i_end):
                    for j in range(max(j_start, i), j_end):
                        if i == j:
                            similarity_matrix[i, j] = 1.0
                        else:
                            sim = self.jaccard_similarity(self.word_sets[i], self.word_sets[j])
                            similarity_matrix[i, j] = sim
                            similarity_matrix[j, i] = sim

                # 强制垃圾回收
                gc.collect()

        return similarity_matrix

    def prepare_features(self):
        """准备基于词汇集合的特征数据"""
        print("开始文本预处理...")

        # 提取问题文本
        texts = [item['content'] for item in self.data]

        # 预处理文本
        self.processed_texts = [self.preprocess_text(text) for text in texts]

        # 过滤空文本
        valid_indices = [i for i, text in enumerate(self.processed_texts) if text.strip()]
        self.processed_texts = [self.processed_texts[i] for i in valid_indices]
        self.data = [self.data[i] for i in valid_indices]

        print(f"预处理完成，有效文本数量: {len(self.processed_texts)}")

        # 提取词汇集合
        print("提取词汇集合...")
        self.word_sets = self.extract_word_sets()

        # 根据数据大小选择计算方法
        n = len(self.word_sets)
        estimated_memory_gb = (n * n * 4) / (1024 ** 3)  # float32需要4字节

        print(f"估计内存需求: {estimated_memory_gb:.1f} GB")

        if estimated_memory_gb > 8:  # 如果超过8GB，使用稀疏矩阵
            print("使用稀疏矩阵方法...")
            self.similarity_matrix = self.compute_sparse_similarity_matrix()
            # 对于稀疏矩阵，距离矩阵需要特殊处理
            self.distance_matrix = None
        elif estimated_memory_gb > 2:  # 如果超过2GB，使用分批处理
            print("使用分批处理方法...")
            self.similarity_matrix = self.compute_batch_similarity_matrix()
            self.distance_matrix = 1 - self.similarity_matrix
        else:  # 小数据集，直接计算
            print("直接计算相似度矩阵...")
            self.similarity_matrix = self._compute_direct_similarity_matrix()
            self.distance_matrix = 1 - self.similarity_matrix

        print("特征准备完成!")

    def _compute_direct_similarity_matrix(self):
        """直接计算相似度矩阵（小数据集）"""
        n = len(self.word_sets)
        similarity_matrix = np.zeros((n, n), dtype=np.float32)

        for i in range(n):
            for j in range(i, n):
                if i == j:
                    similarity_matrix[i][j] = 1.0
                else:
                    sim = self.jaccard_similarity(self.word_sets[i], self.word_sets[j])
                    similarity_matrix[i][j] = sim
                    similarity_matrix[j][i] = sim

        return similarity_matrix

    def find_optimal_eps(self, eps_range=None):
        """
        寻找DBSCAN的最优eps参数

        Args:
            eps_range: eps参数范围，默认基于相似度阈值自动生成
        """
        print("寻找DBSCAN最优eps参数...")

        if eps_range is None:
            # 基于Jaccard相似度阈值生成eps范围
            max_eps = 1 - 0.6  # 最低相似度0.6
            min_eps = 1 - 0.95  # 最高相似度0.95
            eps_range = np.linspace(min_eps, max_eps, 10)  # 减少测试点数

        results = []

        for eps in eps_range:
            print(f"测试 eps={eps:.4f}...")

            # 处理稀疏矩阵的情况
            if hasattr(self.similarity_matrix, 'toarray'):
                # 稀疏矩阵转换为距离矩阵
                distance_matrix = 1 - self.similarity_matrix.toarray()
            else:
                distance_matrix = self.distance_matrix

            # 使用DBSCAN进行聚类
            dbscan = DBSCAN(eps=eps, min_samples=self.min_samples, metric='precomputed')
            cluster_labels = dbscan.fit_predict(distance_matrix)

            # 计算聚类统计信息
            n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
            n_noise = list(cluster_labels).count(-1)
            n_clustered = len(cluster_labels) - n_noise

            results.append({
                'eps': eps,
                'n_clusters': n_clusters,
                'n_noise': n_noise,
                'n_clustered': n_clustered,
                'cluster_ratio': n_clustered / len(cluster_labels) if len(cluster_labels) > 0 else 0
            })

        # 转换为DataFrame
        results_df = pd.DataFrame(results)

        # 选择最优eps
        valid_results = results_df[
            (results_df['n_clusters'] >= 2) &
            (results_df['cluster_ratio'] >= 0.5)  # 至少50%的点被聚类
        ]

        if len(valid_results) > 0:
            # 选择聚类数量适中的eps
            best_idx = valid_results.loc[valid_results['n_clusters'].idxmin()].name
            self.optimal_eps = valid_results.loc[best_idx, 'eps']
        else:
            # 如果没有合适的结果，使用默认值
            self.optimal_eps = 1 - self.jaccard_threshold

        print(f"推荐的最优eps: {self.optimal_eps:.4f}")
        print("\nDBSCAN参数评估结果:")
        print(results_df.round(4))

    def perform_clustering(self, eps=None):
        """
        执行基于词汇相似度的DBSCAN聚类

        Args:
            eps: DBSCAN的eps参数，如果为None则使用最优eps
        """
        if eps is None:
            eps = self.optimal_eps

        print(f"使用DBSCAN进行聚类，eps={eps:.4f}, min_samples={self.min_samples}...")

        # 处理稀疏矩阵的情况
        if hasattr(self.similarity_matrix, 'toarray'):
            # 稀疏矩阵转换为距离矩阵
            distance_matrix = 1 - self.similarity_matrix.toarray()
        else:
            distance_matrix = self.distance_matrix

        # 创建DBSCAN聚类模型
        self.clustering_model = DBSCAN(
            eps=eps,
            min_samples=self.min_samples,
            metric='precomputed'
        )

        # 对距离矩阵进行聚类
        self.cluster_labels = self.clustering_model.fit_predict(distance_matrix)

        # 计算聚类统计信息
        n_clusters = len(set(self.cluster_labels)) - (1 if -1 in self.cluster_labels else 0)
        n_noise = list(self.cluster_labels).count(-1)
        n_clustered = len(self.cluster_labels) - n_noise

        print(f"聚类完成!")
        print(f"聚类数量: {n_clusters}")
        print(f"噪声点数量: {n_noise}")
        print(f"被聚类的点数量: {n_clustered}")
        print(f"聚类比例: {n_clustered / len(self.cluster_labels):.2%}")

        # 统计每个聚类的大小
        cluster_counts = Counter(self.cluster_labels)
        print("\n各聚类大小:")
        for cluster_id in sorted(cluster_counts.keys()):
            if cluster_id == -1:
                print(f"噪声点: {cluster_counts[cluster_id]} 个问题")
            else:
                print(f"聚类 {cluster_id}: {cluster_counts[cluster_id]} 个问题")

    def analyze_clusters(self):
        """分析每个聚类的特征"""
        print("分析聚类特征...")

        cluster_analysis = {}
        unique_clusters = set(self.cluster_labels) - {-1}  # 排除噪声点

        for cluster_id in unique_clusters:
            # 获取该聚类的问题
            cluster_indices = np.where(self.cluster_labels == cluster_id)[0]
            cluster_questions = [self.data[i]['content'] for i in cluster_indices]

            # 获取最常见的词汇
            all_words = []
            for i in cluster_indices:
                all_words.extend(list(self.word_sets[i]))

            word_counts = Counter(all_words)
            top_words = word_counts.most_common(10)

            cluster_analysis[cluster_id] = {
                'size': len(cluster_questions),
                'top_words': top_words,
                'representative_questions': cluster_questions[:5],  # 前5个问题作为代表
                'all_questions': cluster_questions
            }

            print(f"\n=== 聚类 {cluster_id} ===")
            print(f"问题数量: {len(cluster_questions)}")
            print(f"高频词汇: {', '.join([word for word, count in top_words[:5]])}")
            print("代表性问题:")
            for i, q in enumerate(cluster_questions[:3], 1):
                print(f"  {i}. {q}")

        return cluster_analysis

    def save_results(self, cluster_analysis):
        """保存聚类结果"""
        print("保存聚类结果...")

        # 确保输出目录存在
        import os
        output_dir = '/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/out_dbscan'
        os.makedirs(output_dir, exist_ok=True)

        # 为原数据添加聚类标签
        results = []
        for i, item in enumerate(self.data):
            result_item = item.copy()
            result_item['cluster_id'] = int(self.cluster_labels[i])
            result_item['processed_text'] = self.processed_texts[i]
            results.append(result_item)

        # 保存带聚类标签的数据
        with open(f'{output_dir}/biology_qa_dbscan_clustered.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 保存聚类分析结果
        analysis_results = {
            'optimal_eps': self.optimal_eps,
            'total_questions': len(self.data),
            'cluster_analysis': cluster_analysis,
            'algorithm': 'DBSCAN_Jaccard'
        }

        with open(f'{output_dir}/dbscan_analysis_results.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, ensure_ascii=False, indent=2)

        print(f"结果已保存到: {output_dir}")

    def run_complete_analysis(self):
        """运行完整的DBSCAN聚类分析流程"""
        print("开始生物类问答DBSCAN词汇相似度聚类分析...")

        # 1. 加载数据
        if not self.load_data():
            return

        # 2. 准备特征（词汇集合和相似度矩阵）
        self.prepare_features()

        # 3. 寻找最优DBSCAN参数
        self.find_optimal_eps()

        # 4. 执行DBSCAN聚类
        self.perform_clustering()

        # 5. 分析聚类特征
        cluster_analysis = self.analyze_clusters()

        # 6. 保存结果
        self.save_results(cluster_analysis)

        print("\nDBSCAN词汇相似度聚类分析完成!")


def main():
    """主函数"""
    # 数据文件路径
    data_path = "/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/data_biology_only/biology_qa_data.json"

    # 创建分析器并运行分析
    analyzer = BiologyQADBSCANAnalyzer(data_path)
    analyzer.run_complete_analysis()


if __name__ == "__main__":
    main()
