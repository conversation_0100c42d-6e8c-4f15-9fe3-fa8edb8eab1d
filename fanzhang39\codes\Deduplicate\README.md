# 基于TF-IDF+余弦相似度的中文问答文本去重合并系统

## 项目概述

本系统专门用于处理中文问答数据集的文本去重合并，采用TF-IDF向量化和余弦相似度计算，能够识别并合并语义相同但表达顺序不同的问题，同时确保语义不同的问题保持分离。

## 核心特性

### 🎯 严格语义匹配
- **高相似度阈值**：默认0.95，确保只合并真正语义相同的问题
- **避免过度合并**：区分语义相同（词序不同）vs 语义不同（内容不同）
- **精确识别**：基于TF-IDF向量和余弦相似度的数学计算

### 🚀 大规模数据处理
- **分块处理**：支持200万+数据，默认50万条/块
- **内存优化**：使用稀疏矩阵，减少内存占用
- **进度监控**：实时日志记录处理进度

### 🔧 中文文本处理
- **jieba分词**：基础模式，无自定义词典
- **停用词过滤**：使用哈工大停用词表
- **文本标准化**：去除标点、数字，保留中文字符

### 📊 完整输出
- **去重数据集**：合并后的问答数据
- **映射关系**：详细的合并映射记录
- **统计报告**：处理结果和性能统计

## 算法原理

### 1. 文本预处理
```
原始文本 → jieba分词 → 停用词过滤 → 文本标准化 → 处理后文本
```

### 2. TF-IDF向量化
- **TF（词频）**：词在文档中的出现频率
- **IDF（逆文档频率）**：词的区分度权重
- **向量化**：将文本转换为数值向量

### 3. 相似度计算
```
余弦相似度 = (向量A · 向量B) / (|向量A| × |向量B|)
```
- 值域：[0, 1]
- 0.95阈值：确保极高相似度才合并

### 4. 连通分量识别
- 将高相似度的问题对构建图
- 使用连通分量算法识别合并群组
- 每个群组内的问题将被合并

## 合并规则示例

### ✅ 应该合并（语义相同，仅词序不同）
```
"为什么天空是蓝色的？" ↔ "天空为什么是蓝色的？" ↔ "天空为什么蓝？"
"天空什么颜色？" ↔ "天空是什么颜色的？"
```

### ❌ 不应合并（语义不同，即使句式相似）
```
"老虎为什么有条纹？" ≠ "斑马为什么有条纹？" （不同动物）
"为什么人会死？" ≠ "龟壳为什么那么硬？" （完全不同主题）
```

## 技术参数配置

### 核心参数
- **similarity_threshold**: 0.95（相似度阈值）
- **chunk_size**: 500,000（分块大小）
- **max_features**: 50,000（TF-IDF最大特征数）
- **min_df**: 2（最小文档频率）
- **max_df**: 0.95（最大文档频率）
- **ngram_range**: (1, 2)（1-2gram特征）

### TF-IDF配置
```python
TfidfVectorizer(
    max_features=50000,      # 最大特征数
    min_df=2,                # 最小文档频率
    max_df=0.95,             # 最大文档频率
    ngram_range=(1, 2),      # 1-2gram特征
    token_pattern=r'[\u4e00-\u9fff]+',  # 只保留中文字符
)
```

## 使用方法

### 1. 环境准备
```bash
pip install numpy pandas scikit-learn scipy jieba
```

### 2. 正常运行
```bash
cd D:\ProJects\kexue\fanzhang39\codes\Deduplicate
python text_deduplication_tfidf.py
```

### 3. 测试模式
```bash
python text_deduplication_tfidf.py test
```

### 4. 自定义配置
```python
from text_deduplication_tfidf import TextDeduplicationTFIDF

# 创建实例
deduplicator = TextDeduplicationTFIDF(
    similarity_threshold=0.95,  # 相似度阈值
    chunk_size=500000          # 分块大小
)

# 运行去重
deduplicator.run_deduplication(
    data_path="your_data.json",
    stopwords_path="stopwords.txt", 
    output_dir="output_directory"
)
```

## 输入输出格式

### 输入数据格式
```json
[
  {
    "id": "1",
    "content": "为什么天空是蓝色的？",
    "label": "物理"
  },
  {
    "id": "2", 
    "content": "天空为什么是蓝色的？",
    "label": "物理"
  }
]
```

### 输出文件
1. **deduplicated_qa_dataset.json**：去重后的问答数据集
2. **merge_mapping.json**：详细合并映射关系
3. **deduplication_report.txt**：统计分析报告
4. **processing_log.txt**：处理过程日志

### 合并映射格式
```json
{
  "1": {
    "kept_question": {
      "id": "1",
      "content": "为什么天空是蓝色的？",
      "label": "物理"
    },
    "merged_questions": [
      {
        "id": "2",
        "content": "天空为什么是蓝色的？", 
        "label": "物理"
      }
    ],
    "merge_count": 1
  }
}
```

## 性能特征

### 内存优化
- **稀疏矩阵**：TF-IDF和相似度矩阵使用稀疏存储
- **分块处理**：避免一次性加载全部数据
- **及时释放**：处理完成后立即释放内存

### 计算复杂度
- **时间复杂度**：O(n²) 对于相似度计算
- **空间复杂度**：O(n×f) 其中f为特征数
- **优化策略**：阈值过滤减少实际计算量

### 处理能力
- **数据规模**：支持200万+记录
- **处理速度**：约1000-5000条/秒（取决于硬件）
- **内存需求**：8-16GB推荐（50万条/块）

## 质量保证

### 算法验证
- 内置测试数据验证合并效果
- 严格的相似度阈值控制
- 详细的处理日志和统计报告

### 错误处理
- 完整的异常捕获和日志记录
- 数据格式验证
- 内存不足保护机制

### 可追溯性
- 完整的合并映射记录
- 详细的统计报告
- 处理过程日志

## 注意事项

1. **内存需求**：大数据集需要足够内存，建议16GB+
2. **处理时间**：200万数据约需要1-3小时
3. **阈值调整**：可根据实际需求调整相似度阈值
4. **中文分词**：依赖jieba分词质量
5. **停用词**：使用哈工大停用词表，可自定义

## 技术支持

如有问题或建议，请查看处理日志文件或联系开发团队。
