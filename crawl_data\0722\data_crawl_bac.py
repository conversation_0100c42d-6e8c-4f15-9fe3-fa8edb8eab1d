# 13个类型爬取，用20w条数据爬取，把13个类型的定义加内容要求（包括各个学段的具体内容）都给模型，让模型判断问题超不超纲，不超纲判断所属核心概念
from fanzhang39.codes.qa.build_input_pq_clean import fewshot_input

concept1= """【物质的结构与性质】
学段：1-2年级
- **学习内容1.1：物质具有一定的特性与功能**
  - 内容要求：
    - ①观察并描述物体的轻重、薄厚、颜色、表面粗糙程度、形状等外部特征，能根据物体的外部特征对其进行简单分类。
    - ②识别生活中常见的材料。
- **学习内容1.2：空气与水是重要的物质**
  - 内容要求：
    - ③认识空气是无色、无味的气体。
    - ④观察并描述水的颜色、状态、气味等特征。
- **学习内容1.3：金属及合金是重要的材料**
  - 内容要求：
    - ⑤举例说出生活中常见的金属，知道金属是常见的材料。

学段：3-4年级
- **学习内容1.1：物质具有一定的特性与功能**
  - 内容要求：
    - ①能使用简单的仪器测量一些物体的长度、质量、体积、温度等常见特征，并使用恰当的计量单位进行记录。
    - ②能根据物体的特征或材料的性质将两种混合在一起的物体分离开来，如分离沙和糖、铁屑和木屑等。
    - ③描述某些材料的透光性、导电性，说出它们的主要用途。
- **学习内容1.2：空气与水是重要的物质**
  - 内容要求：
    - ④说明空气有质量并占有一定的空间，空气会充满各处。
    - ⑤观察并描述空气受热上升的现象。
    - ⑥知道风是一种空气流动的现象，列举生活中常见的形成风的一些方法。
    - ⑦说出冰、水、水蒸气在状态和体积等方面的区别，知道三者虽然状态不同，但都是同一种物质。
    - ⑧观察并描述水沸腾或结冰的现象；了解一般状况下，水沸腾和结冰时的温度，知道温度是影响水沸腾和结冰的重要因素。

学段：5-6年级
- **学习内容1.1：物质具有一定的特性与功能**
  - 内容要求：
    - ①观察常见材料在水中的沉浮现象、导热性等，说出它们的主要用途。
- **学习内容1.2：空气与水是重要的物质**
  - 内容要求：
    - ②知道空气是一种混合物，含有氮气、氧气、二氧化碳等气体，空气中的氧气和二氧化碳对生命活动具有重要意义。
    - ③列举日常生活中水的蒸发和水蒸气凝结成水的实例，如晒衣服、雾、玻璃窗上的水珠等。"""
concept2="""【物质的变化与化学反应】
学段：1-2年级
- **学习内容2.2：物质的溶解和溶液**
  - 内容要求：
    - ①知道有些物质能溶解在水中，如食盐和白糖等；有些物质很难溶解在水中，如沙和食用油等。

学段：3-4年级
- **学习内容2.1：物质的三态变化**
  - 内容要求：
    - ①知道固体有确定的形状、体积和质量；液体有确定的体积和质量，液体静止时其表面一般会保持水平；气体有确定的质量，但没有确定的体积和形状。
    - ②描述加热或冷却时常见物质发生的状态变化，如水结冰、冰熔化、水蒸发和水蒸气凝结。
- **学习内容2.2：物质的溶解和溶液**
  - 内容要求：
    - ③描述一定量的不同物质在一定量水中的溶解情况。
    - ④知道是否搅拌和温度高低是影响物质在水中溶解快慢的常见因素。
- **学习内容2.3：物质变化的特征**
  - 内容要求：
    - ⑤知道有些物体的形状或大小发生了变化，如被切成小块、被挤压、被拉伸等，构成物体的物质没有改变。

学段：5-6年级
- **学习内容2.3：物质变化的特征**
  - 内容要求：
    - ①知道有些物体发生了变化，如纸燃烧、铁生锈等，构成物体的物质也发生了改变。"""
concept3="""【物质的运动与相互作用】
学段：1-2年级
- **学习内容3.1：力是改变物体运动状态的原因**
  - 内容要求：
    - ①使用前后左右、东南西北、上下、远近等描述物体所处的位置和方向。
    - ②知道推力和拉力是常见的力，力可以使物体的形状发生改变。
- **学习内容3.2：电磁相互作用**
  - 内容要求：
    - ③列举常用的不同形状的磁铁。
    - ④知道磁铁可以直接或隔着一段距离对铁、镍等材料产生吸引作用。

学段：3-4年级
- **学习内容3.1：力是改变物体运动状态的原因**
  - 内容要求：
    - ①知道可以用相对于另一个物体的方向和距离来描述运动物体在某个时刻的位置。
    - ②知道测量距离和时间的常用方法。
    - ③知道用速度的大小来描述物体运动的快慢，知道自行车、火车、飞机等常用交通工具的大致速度。
    - ④描述生活中常见物体的直线运动、曲线运动等运动方式，比较不同的运动，举例说明各种运动的形式和特征。
- **学习内容3.2：电磁相互作用**
  - 内容要求：
    - ⑤知道磁铁同时存在两个不同的磁极，相同的磁极互相排斥，不同的磁极互相吸引。
    - ⑥知道地球有磁场，指南针中的小磁针可以用来指示南北。
    - ⑦知道电源、导线、用电器和开关是构成电路的必要元件；说明形成电路的条件，切断闭合回路是控制电流的一种方法。
    - ⑧知道有些材料是导体，容易导电；有些材料是绝缘体，不容易导电。
    - ⑨列举电的重要用途，知道雷电、高压电、家庭电路中的交流电会对人体产生伤害，知道安全用电的常识。
- **学习内容3.3：声音与光的传播**
  - 内容要求：
    - ⑩识别来自光源的光（如太阳光、灯光）或来自物体反射的光（如月光）。
    - ⑪描述光被阻挡时形成阻挡物影子的现象。
    - ⑫举例说明声音因物体的振动而产生。
    - ⑬举例说明声音在不同物质中可以向各个方向传播。
    - ⑭知道声音有高低和强弱之分，声音高低和强弱的变化是由于振动的变化引起的；制作能产生不同高低、强弱声音的简易装置。
    - ⑮知道噪声的危害和防治，学会保护听力的常用方法。

学段：5-6年级
- **学习内容3.1：力是改变物体运动状态的原因**
  - 内容要求：
    - ①知道日常生活中常见的摩擦力、弹力、浮力等都是直接施加在物体上的力。
    - ②举例说明给物体施加力可以改变物体运动的快慢，也可以使物体开始或停止运动；学会使用弹簧测力计。
    - ③知道地球上一切物体都受到地球的吸引力，地球不需要接触物体就可以对其施加引力。
- **学习内容3.3：声音与光的传播**
  - 内容要求：
    - ④知道来自光源的光或来自物体的反射光进入眼睛，能使人们看到光源或该物体。
    - ⑤知道光在空气中沿直线传播。
    - ⑥知道光遇到物体会发生反射现象，光的传播方向会发生改变。
    - ⑦描述太阳光穿过三棱镜后形成的彩色光带，知道太阳光中包含不同颜色的光。"""
concept4="""【能的转化与能量守恒】
学段：3-4年级
- **学习内容4.1：能的形式、转移与转化**
  - 内容要求：
    - ①了解生活中各种能的形式，知道运动的物体具有能量。
    - ②描述测量物体温度的方法，知道摄氏度是表示物体冷热程度的常用温度标准单位。
    - ③知道物体一般具有热胀冷缩的性质，知道水结冰时体积会膨胀。

学段：5-6年级
- **学习内容4.1：能的形式、转移与转化**
  - 内容要求：
    - ①知道动能、声能、光能、热能、电能、磁能等都是能的形式，了解这些能的相互转化现象。
    - ②知道简单机械（杠杆、滑轮、轮轴、斜面）及其在生产生活中的应用。
    - ③举例说出生活中常见的热传递现象，知道热从温度高的物体传向温度低的物体，从物体温度高的部分传向温度低的部分。
    - ④举例说明影响热传递的主要因素，列举它们在日常生产生活中的应用。
- **学习内容4.2：能源与可持续发展**
  - 内容要求：
    - ⑤了解太阳能、水能、风能、地热能、化石能等能源。"""
concept5="""【生命系统的构成层次】
学段：1-2年级
- **学习内容5.1：生物具有区别于非生物的特征**
  - 内容要求：
    - ①举例说明动物和植物都是生物。
- **学习内容5.2：地球上存在动物、植物、微生物等不同类型的生物**
  - 内容要求：
    - ②说出生活中常见动物的名称及特征，说出动物的某些共同特征（如都会运动）。
    - ③说出周围常见植物的名称及特征。
- **学习内容5.5：人体由多个系统组成**
  - 内容要求：
    - ④识别人的眼、耳、鼻、舌、皮肤等器官，列举这些器官的功能与保护方法。

学段：3-4年级
- **学习内容5.1：生物具有区别于非生物的特征**
  - 内容要求：
    - ①说出生物与非生物的不同特点，描述生物的特征。
- **学习内容5.2：地球上存在动物、植物、微生物等不同类型的生物**
  - 内容要求：
    - ②根据某些特征，对动物进行分类。
    - ③识别常见的动物类别，描述某一类动物（如昆虫、鱼类、鸟类、哺乳类）的共同特征；列举几种我国的珍稀动物。
    - ④说出植物的某些共同特征；列举当地的植物资源，尤其是与人类生活密切相关的植物。
- **学习内容5.4：生物体具有一定的结构层次**
  - 内容要求：
    - ⑤描述植物一般由根、茎、叶、花、果实和种子构成。
- **学习内容5.5：人体由多个系统组成**
  - 内容要求：
    - ⑥描述人体用于呼吸的器官，列举保护这些器官的方法。
    - ⑦描述人体用于摄取养分的器官，列举保护这些器官的方法。
- **学习内容5.6：生态系统由生物与非生物环境共同组成**
  - 内容要求：
    - ⑧举例说出水、阳光、空气、温度的变化对生物生存的影响。
    - ⑨列举动物依赖植物筑巢或作为庇护所的实例。

学段：5-6年级
- **学习内容5.2：地球上存在动物、植物、微生物等不同类型的生物**
  - 内容要求：
    - ①列举生活中常见的微生物（如酵母菌、霉菌、病毒），举例说出感冒、痢疾等疾病是由微生物引起的。
    - ②根据某些特征，对植物进行分类。
- **学习内容5.3：细胞是生物体结构与生命活动的基本单位**
  - 内容要求：
    - ③初步学会使用显微镜观察细胞，知道细胞是生物体的基本结构单位。
- **学习内容5.5：人体由多个系统组成**
  - 内容要求：
    - ④说出脑是认知、情感、意志和行为的生物学基础，举例说出保护脑健康的主要措施。
- **学习内容5.6：生态系统由生物与非生物环境共同组成**
  - 内容要求：
    - ⑤举例说出常见的栖息地为生物提供光、空气、水、适宜的温度和食物等基本条件。
    - ⑥说出常见动物和植物之间吃与被吃的链状关系。"""
concept6="""【生物体的稳态与调节】
学段：1-2年级
- **学习内容6.1：植物能制造和获取养分来维持自身的生存**
  - 内容要求：
    - ①说出植物的生存和生长需要水、阳光和空气。
- **学习内容6.2：人和动物通过获取其他生物的养分来维持生存**
  - 内容要求：
    - ②举例说出动物可以通过眼、耳、鼻等器官感知环境。

学段：3-4年级
- **学习内容6.1：植物能制造和获取养分来维持自身的生存**
  - 内容要求：
    - ①描述植物的生存和生长需要水、阳光、空气和适宜的温度。
    - ②描述植物的根、茎、叶、花、果实和种子具有帮助植物维持自身生存的相应功能。
- **学习内容6.2：人和动物通过获取其他生物的养分来维持生存**
  - 内容要求：
    - ③举例说出动物通过皮肤、四肢、翼、鳍、鳃等接触和感知环境。
    - ④描述动物维持生命需要空气、水、食物和适宜的温度。

学段：5-6年级
- **学习内容6.1：植物能制造和获取养分来维持自身的生存**
  - 内容要求：
    - ①知道植物可以利用阳光、空气和水分在绿色叶片中制造其生存所需的养分。
- **学习内容6.2：人和动物通过获取其他生物的养分来维持生存**
  - 内容要求：
    - ②知道动物以其他生物为食，动物维持生命需要消耗这些食物而获得能量。
    - ③说出人体生长发育所需的主要营养物质及其消化吸收过程。
- **学习内容6.3：人体通过一定的调节机制保持稳态**
  - 内容要求：
    - ④举例说出人体对某些环境刺激的反应方式和作用，列举保护相关器官的方法。"""
concept7="""【生物与环境的相互关系】
学段：3-4年级
- **学习内容7.1：生物能适应其生存环境**
  - 内容要求：
    - ①举例说出生活在不同环境中的植物的外部形态具有不同的特点，以及这些特点对维持植物生存的作用。
    - ②举例说出动物适应季节变化的方式，说出这些变化对维持动物生存的作用。

学段：5-6年级
- **学习内容7.1：生物能适应其生存环境**
  - 内容要求：
    - ①举例说出动物在气候、食物、空气和水源等环境变化时的行为。
- **学习内容7.3：人的生活习惯影响机体健康**
  - 内容要求：
    - ②列举睡眠、饮食、运动等影响健康的因素，养成良好的生活习惯。
- **学习内容7.4：人体生命安全与生存环境密切相关**
  - 内容要求：
    - ③举例说出重大传染病和突发公共卫生事件对人类安全的威胁。

学段：5-6年级
- **学习内容7.1：生物能适应其生存环境**
  - 内容要求：
    - ①举例说出动物在气候、食物、空气和水源等环境变化时的行为。
- **学习内容7.3：人的生活习惯影响机体健康**
  - 内容要求：
    - ②列举睡眠、饮食、运动等影响健康的因素，养成良好的生活习惯。
- **学习内容7.4：人体生命安全与生存环境密切相关**
  - 内容要求：
    - ③举例说出重大传染病和突发公共卫生事件对人类安全的威胁。"""
concept8="""【生命的延续与进化】
学段：3-4年级
- **学习内容8.1：植物通过多种方式进行繁殖**
  - 内容要求：
    - ①举例说出植物从生到死的生命过程；举例说出植物通常会经历由种子萌发成幼苗，再到开花、结出果实和种子的过程。
    - ②描述有的植物通过产生种子繁殖后代，有的植物通过根、茎、叶等繁殖后代。
    - ③列举动物帮助植物传粉或传播种子的实例。
- **学习内容8.2：不同种类动物具有不同的生殖方式和发育过程**
  - 内容要求：
    - ④举例说出动物从生到死的生命过程。
    - ⑤描述和比较胎生与卵生动物的繁殖方式。

学段：5-6年级
- **学习内容8.3：人的生命是从受精卵开始的**
  - 内容要求：
    - ①认识青春期及其特征，关注青春期保健。
- **学习内容8.5：生物体的遗传信息逐代传递，可发生改变**
  - 内容要求：
    - ②描述和比较植物子代与亲代在形态特征方面的异同。
    - ③描述和比较动物子代与亲代在形态特征方面的异同。
- **学习内容8.6：生物的遗传变异和环境因素的共同作用导致了生物的进化**
  - 内容要求：
    - ④根据化石资料，举例说出已灭绝的生物；描述和比较灭绝生物与当今某些生物的相似之处。"""
concept9="""【宇宙中的地球】
学段：1-2年级
- **学习内容9.2：地球绕地轴自转**
  - 内容要求：
    - ①观察并描述太阳每天在天空中东升西落的位置变化，初步学会根据太阳的位置辨认方向。
- **学习内容9.3：地球围绕太阳公转**
  - 内容要求：
    - ②描述一年中季节变化的现象，举例说出季节变化对动植物和人们生活的影响。
- **学习内容9.4：月球是地球的卫星**
  - 内容要求：
    - ③知道每天观察到的月亮形状是变化的。

学段：3-4年级
- **学习内容9.1：地球是一颗行星**
  - 内容要求：
    - ①知道地球是一个球体，是太阳系中的一颗行星，太阳系有八颗行星。
- **学习内容9.2：地球绕地轴自转**
  - 内容要求：
    - ②观察并描述太阳光照射下物体影长从早到晚的变化情况。
- **学习内容9.4：月球是地球的卫星**
  - 内容要求：
    - ③知道月球是地球的天然卫星；通过望远镜观察或利用图片资料，了解月球表面的概况。

学段：5-6年级
- **学习内容9.2：地球绕地轴自转**
  - 内容要求：
    - ①知道地球的自转轴、自转周期和自转方向，理解昼夜交替和天体东升西落等自然现象与地球的自转有关。
- **学习内容9.3：地球围绕太阳公转**
  - 内容要求：
    - ②知道地球围绕太阳公转的周期和方向，理解四季的形成与地球的公转有关。
    - ③测量正午时物体的影长，说明不同季节正午影长的变化情况。
- **学习内容9.4：月球是地球的卫星**
  - 内容要求：
    - ④知道新月、上弦月、满月、下弦月四种月相，说明月相的变化情况。
- **学习内容9.5：地球所处的宇宙环境**
  - 内容要求：
    - ⑤比较太阳、地球、月球的相对大小，知道太阳是一颗恒星，是太阳系的中心天体，描述太阳系八颗行星在太阳系中的相对位置。
    - ⑥知道宇宙中有很多恒星，通过观察或借助软件识别织女星、牛郎星等亮星，学会利用北极星辨认方向；知道大熊座、猎户座等星座，了解划分星座的意义。
- **学习内容9.6：太空探索拓展了人类对宇宙的认知**
  - 内容要求：
    - ⑦了解人造卫星和载人航天的历史，知道太空环境对人体健康的影响，关注我国航天事业的进展。
    - ⑧了解天文观测和利用航天器探测宇宙的历史，关注我国月球和深空探测事业的进展。"""
concept10="""【地球系统】
学段：1-2年级
- **学习内容10.1：天气和气候**
  - 内容要求：
    - ①知道阴、晴、雨、雪、风等天气现象。
    - ②描述天气变化对动植物和人类生活的影响。
- **学习内容10.3：岩石和土壤**
  - 内容要求：
    - ③知道土壤为众多动植物提供了生存场所。

学段：3-4年级
- **学习内容10.1：天气和气候**
  - 内容要求：
    - ①知道地球表面被大气包围着，大气是运动的；学会使用气温计测量气温，并描述一天中气温的变化。
    - ②学会使用仪器测量和记录气温、风力、风向、降水量等气象数据，并运用测量结果描述天气状况；识别常用的天气符号，理解天气预报用语。
- **学习内容10.2：水循环**
  - 内容要求：
    - ③知道地球表面的海陆分布情况，说出河流、湖泊、海洋、冰川、地下水等主要水体类型。
- **学习内容10.3：岩石和土壤**
  - 内容要求：
    - ④知道土壤的主要成分，观察并描述砂质土、黏质土、壤质土的特点，举例说出它们适宜生长的植物。

学段：5-6年级
- **学习内容10.1：天气和气候**
  - 内容要求：
    - ①知道雨、雪、雾等天气现象的成因。
- **学习内容10.2：水循环**
  - 内容要求：
    - ②知道水在改变地表形态的过程中发挥着重要作用。
- **学习内容10.3：岩石和土壤**
  - 内容要求：
    - ③知道地球表面覆盖着岩石，岩石是由矿物组成的；学会通过观察和使用简单工具，比较不同岩石的颜色、坚硬程度、颗粒粗细等特征。
- **学习内容10.4：地球内部圈层和地壳运动**
  - 内容要求：
    - ④知道地球内部分为地壳、地幔和地核，地壳主要由岩浆岩、沉积岩和变质岩构成，了解化石的形成及科学价值；知道火山喷发和地震是地球内部能量集中释放产生的自然现象。"""
concept11="""【人类活动与环境】
学段：1-2年级
- **学习内容11.3：人类活动对环境的影响**
  - 内容要求：
    - ①举例说出人类的生活与自然环境有关，知道地球是人类与动植物共同的家园。
    - ②知道有些材料可以被回收利用，树立节约资源、保护环境的意识。

学段：3-4年级
- **学习内容11.1：自然资源**
  - 内容要求：
    - ①说出人类利用矿产资源进行工业生产的例子，树立合理利用矿产资源的意识。
    - ②说出人类生活离不开水的例子，树立节约用水的意识。
    - ③知道土壤是农业生产的基础，树立保护土壤资源的意识。
- **学习内容11.3：人类活动对环境的影响**
  - 内容要求：
    - ④有些废旧材料可以被分类和回收。

学段：5-6年级
- **学习内容11.1：自然资源**
  - 内容要求：
    - ①知道海洋为人类生存提供了能源、生物资源、矿产资源等，树立海洋国土意识。
    - ②知道资源可分为可再生资源和不可再生资源；以垃圾分类为例，通过践行垃圾减量与分类回收，树立循环利用资源的意识。
- **学习内容11.2：自然灾害**
  - 内容要求：
    - ③结合实例，知道台风、洪涝、干旱、沙尘暴、泥石流等灾害及其影响，树立自我保护和防灾减灾的意识。
- **学习内容11.3：人类活动对环境的影响**
  - 内容要求：
    - ④正确认识经济发展和生态环境保护的关系，结合实例，说明人类不合理的开发活动对环境的影响，提出保护环境的建议，参与保护环境的行动。
    - ⑤关注野生动物和濒危植物的保护，拒绝濒危动植物及其产品贸易，认识到保护生物多样性的重要性。"""
concept12="""【技术、工程与社会】
学段：1-2年级
- **学习内容12.1：技术与工程创造了人造物，技术的核心是发明，工程的核心是建造**
  - 内容要求：
    - ①知道我们周围的人造物是由人设计并制造出来的，观察和区别身边的自然物和人造物。
    - ②学会使用锤子、安全剪刀、放大镜等简单工具；应用身边的材料和工具，制作简单的手工作品。
- **学习内容12.2：技术与工程改变了人们的生产和生活**
  - 内容要求：
    - ③举例说出周围简单科技产品的结构和功能，知道科技产品给人们生活带来的便利、快捷和舒适。
- **学习内容12.3：科学、技术、工程相互影响与促进**
  - 内容要求：
    - ④初步体验利用工具可以更好地进行观察与测量。

学段：3-4年级
- **学习内容12.1：技术与工程创造了人造物，技术的核心是发明，工程的核心是建造**
  - 内容要求：
    - ①区别生活中常见的天然材料和人造材料，说出中国古代技术与工程方面的典型案例。
    - ②举例说出工具在生产和生活中的应用，知道使用工具可以更加便利、快捷和精确；学会使用常见的工具制作简单作品；拆装简单产品，了解产品的构造和特点。
- **学习内容12.2：技术与工程改变了人们的生产和生活**
  - 内容要求：
    - ③举例说出一些典型的技术（如交通技术、电力技术等）和工程（如高速铁路、发电站等）对人们生活的影响；尝试设计和制作某种产品的简化实物模型，并反映其中的部分科学原理。
- **学习内容12.3：科学、技术、工程相互影响与促进**
  - 内容要求：
    - ④初步说明一些技术产品涉及的科学概念或原理，尝试应用科学原理设计并制作简易装置（如传声器、听诊器等）。

学段：5-6年级
- **学习内容12.1：技术与工程创造了人造物，技术的核心是发明，工程的核心是建造**
  - 内容要求：
    - ①知道技术包括方法、程序和产品等；知道发明的常用方法，举例说出一些典型的发明，知道发明会用到一定的科学原理，很多发明可以在自然界找到原型。
    - ②知道工程以科学和技术为基础，知道工程通常由多个系统组成；知道中国主要的大科学工程。
- **学习内容12.2：技术与工程改变了人们的生产和生活**
  - 内容要求：
    - ③知道技术对提高生产效率或工作效率的影响，举例说明应用适当技术可以提高生产效率或工作效率，应用所学科学原理设计并制作出可以提高效率的作品。
- **学习内容12.3：科学、技术、工程相互影响与促进**
  - 内容要求：
    - ④初步认识技术与工程对科学发展的促进作用，应用仪器设备进行观察并进行记录；举例说明科学发现可以促进新技术发明（如激光的发明）。"""
concept13="""【工程设计与物化】
学段：1-2年级
- **学习内容13.1：工程需要定义和界定**
  - 内容要求：
    - ①通过观察，提出并描述简单的制作问题。
- **学习内容13.2：工程的关键是设计**
  - 内容要求：
    - ②学会使用简单的草图，说出自己的思路。
- **学习内容13.3：工程是设计方案物化的结果**
  - 内容要求：
    - ③学会使用简单的工具，对生活中常见的材料进行简单的加工处理。
    - ④制作简单的实物模型并展示，尝试通过观察发现作品中存在的问题并提出改进方案。

学段：3-4年级
- **学习内容13.1：工程需要定义和界定**
  - 内容要求：
    - ①描述简单的设计问题，包括材料、时间或成本等限制条件。
- **学习内容13.2：工程的关键是设计**
  - 内容要求：
    - ②借助表格、草图、实物模型、戏剧或故事等方式说明自己的设计思路。
    - ③根据需求和限制条件，比较多种可能的解决方案，并初步判断其合理性。
- **学习内容13.3：工程是设计方案物化的结果**
  - 内容要求：
    - ④利用常用工具，对常见材料进行简单加工处理。
    - ⑤知道制作过程应遵循一定的顺序，制作简单的实物模型；尝试发现实物模型的不足，改进并展示。

学段：5-6年级
- **学习内容13.1：工程需要定义和界定**
  - 内容要求：
    - ①定义简单工程问题，包括材料、时间或成本等限制条件，提出验收标准。
- **学习内容13.2：工程的关键是设计**
  - 内容要求：
    - ②利用示意图、影像、文字或实物等多种方式，阐明自己的创意，初步认识设计方案中各影响因素间的关系。
    - ③基于有说服力的论证，认同或质疑某些设计方案，并初步判断其可行性和合理性。
- **学习内容13.3：工程是设计方案物化的结果**
  - 内容要求：
    - ④利用工具制作简单的实物模型，根据实际反馈结果进行改进并展示。"""

#
#
# fewshot_input = [
#     {"id": "0", "content": "黑洞是什么？"},
#     {"id": "1", "content": "火山爆发是因为什么？为什么头发变白？"},
#     {"id": "2", "content": "这个枪的原理是什么？"},
#     {"id": "3", "content": "为什么切开的梨放一会会变黑？"},
#     {"id": "4", "content": "那是引力场。"},
#     {"id": "5", "content": "铝热反应的方程式。"},
#     {"id": "6", "content": "蜘蛛为什么会飞？"}
# ]
# fewshot_output = [
#     {"id": "0", "type": "正常", "label": "其他"},
#     {"id": "1", "type": "涉及多个提问", "label": ""},
#     {"id": "2", "type": "指代不清淅", "label": ""},
#     {"id": "3", "type": "正常", "label": "物质的变化与化学反应"},
#     {"id": "4", "type": "意图不明确", "label": ""},
#     {"id": "5", "type": "超纲问题", "label": "物质的变化与化学反应"},
#     {"id": "6", "type": "存在科学错误", "label": ""}
# ]
# merged_concept = ""
# for i in range(1, 14):
#     var_name = f"concept{i}"
#     # 使用eval获取变量的值
#     merged_concept += eval(var_name) + "\n"
# # 13个类型爬取，用20w条数据爬取，把13个类型的定义加内容要求（包括各个学段的具体内容）都给模型，让模型判断问题超不超纲，不超纲判断所属核心概念
# def build_prompt(input):
#     return f'''
# # 任务描述
# 假设你是一位科学教师，下面是一组科学问题列表，首先判断它是否是一个正常的科学问题，如果出现【异常情况类型】中的情况，则直接在type中给出其具体属于哪种异常情况，label为空字符串，否则type输出为”正常“。
# 然后参考【学科核心概念定义】，仔细阅读并理解问题列表中的每一个问题，判断其是否超纲（是否出现了非学习内容要求中的内容），若超纲则在type字段输出”超纲问题“，并正常参考核心概念定义输出其所属核心的概念类型；若不超纲则判断其具体属于学科核心概念的哪种类别，如果都不是的话，label输出为“其他”即可。
# 请参考样例，以json形式直接输出，并且严格按照样例输出格式，保证输出与输入数量一致，且严格一一对应，不要输出其他内容！
#
# # 异常情况类型
# -存在科学错误
# -意图不明确
# -指代不清淅
# -涉及多个提问
# -超纲问题
#
# # 学科核心概念定义
# {merged_concept}
#
# # 样例
# ## 问题列表
# {fewshot_input}
# ## 输出
# {fewshot_output}
#
# # 当前输入
# ## 问题列表
# {input}
#
# # 任务重述
# 请参考样例，并以json形式输出问题列表中的每一个问题的类型与标签。
# '''
#
#
# import json
#
# total = 0
# res = []
# tmp = []
# i = 0
#
# with open("merged_datas_round1_0.95.json", encoding='utf-8') as f:
#     all_data = json.load(f)  # 加载整个 JSON 列表
#
# # 串行处理数据
# for data in all_data:
#     total += 1
#     tmp.append({"id": data['id'], "content": data['content']})
#     if len(tmp) == 10:
#         d = {}
#         d['id'] = f'{i}-{len(tmp)}'
#         d['query'] = build_prompt(tmp).strip()  # 假设build_prompt函数已定义
#         res.append(d)
#         tmp = []
#         i += 1
#
# # 处理剩余数据
# if tmp:
#     d = {}
#     d['id'] = f'{i}-{len(tmp)}'
#     d['query'] = build_prompt(tmp).strip()
#     res.append(d)
# print(len(res))
# print(build_prompt(tmp).strip())
# with open("sci_crawl_13type.json", 'w', encoding='utf-8') as f:
#     for item in res:
#         f.write(json.dumps(item, ensure_ascii=False) + '\n')






#
# # 处理工单系统中的数据
# import json
# import re
# def extract_questions(query_text):
#     """从query文本中提取【当前输入】的问题列表"""
#     # 匹配"## 当前输入\n## 问题列表"后面的列表
#     # 使用非贪婪匹配提取第一个出现的[]内容
#     pattern = r'# 当前输入\n## 问题列表\n(\[.*?\])\n\n# 任务重述'
#     match = re.search(pattern, query_text, re.DOTALL)  # re.DOTALL让.匹配换行符
#     if match:
#         try:
#             questions_str = match.group(1)
#             # print(questions_str)
#             # 将单引号替换为双引号，确保JSON格式正确
#             questions_str = questions_str.replace("'", "\"")
#             return json.loads(questions_str)
#         except json.JSONDecodeError as e:
#             print(f"解析问题列表失败：{e}")
#             return []
#     print("未找到当前输入的问题列表")
#     return []
#
#
# def extract_answers(answer_text):
#     """从answer字段提取答案列表"""
#     try:
#
#         return json.loads(repace_yh(answer_text))
#     except json.JSONDecodeError as e:
#         print(f"解析答案列表失败：{e}")
#         print(answer_text)
#         return []
#
# def repace_yh(text):
#     return text.replace("{'", '''{"''').replace(": '", ''': "''').replace("'}", '''"}''').replace("':", '''":''').replace(", '", ''', "''').replace("',", '''",''').strip()
#
# def process_data(input_file, output_file):
#     """处理数据并生成结果"""
#     results = []
#     type_counts = [0, 0, 0, 0, 0, 0]
#     label_counts = [0,0,0,0,0,0,0,0,0,0,0,0,0,0]
#     type_mapping = {
#     '正常': 0,
#     '存在科学错误': 1,
#     '意图不明确': 2,
#     '指代不清淅': 3,
#     '涉及多个提问': 4,
#     '超纲问题': 5
# }
#     label_mapping = {
#     '物质的结构与性质': 0,
#     '物质的变化与化学反应': 1,
#     '物质的运动与相互作用': 2,
#     '能的转化与能量守恒': 3,
#     '生命系统的构成层次': 4,
#     '生物体的稳态与调节': 5,
#     '生物与环境的相互关系': 6,
#     '生命的延续与进化': 7,
#     '宇宙中的地球': 8,
#     '地球系统': 9,
#     '人类活动与环境': 10,
#     '技术、工程与社会': 11,
#     '工程设计与物化': 12,
#     '其他': 13,
# }
#     # 读取输入文件（每行一个JSON对象）
#     with open(input_file, 'r', encoding='utf-8') as f:
#         for line in f:
#             data = json.loads(line.strip())
#             # print(data)
#
#             query = data['query']
#             # print(query)
#             answer = data['answer']
#             answer = repace_yh(answer)
#             # print(answer)
#
#
#             # 提取当前输入的问题列表（自动限制在前20个）
#             questions = extract_questions(query)
#             # 提取答案列表
#             answers = extract_answers(answer)
#             # print(answers)
#
#             # 将答案转为id映射（用字符串id避免数字/字符串类型问题）
#             answer_map = {str(ans['id']): ans for ans in answers}
#             # print(answer_map)
#
#
#             try:
#                 # 遍历问题列表，匹配id并过滤非科学数据
#                 for question in questions:
#                     q_id = str(question.get('id'))
#                     # print(q_id)
#                     content = question.get('content')
#                     # print(content)
#
#                     if q_id in answer_map:
#                         type_name = answer_map[q_id].get('type')
#                         label_name = answer_map[q_id].get('label')
#
#                         # 统计数量
#                         type_index = type_mapping[type_name]
#                         type_counts[type_index] += 1
#                         # if type_name == '正常':
#
#
#                         # 解析非超纲概念
#                         if type_name == '正常' and type_name != '' and (label_name in label_mapping):  # 排除非科学数据
#                             label_index = label_mapping[label_name]
#                             label_counts[label_index] += 1
#
#                             results.append({
#                                 'id': q_id,
#                                 'content': content,
#                                 'label': label_name,
#                             })
#
#                         # # 解析全部数据
#                         # if type_name in type_mapping:  # 排除非科学数据
#                         #     if type_name=="正常" or type_name=="超纲问题":
#                         #         # print(q_id)
#                         #         label_index = label_mapping[label_name]
#                         #         label_counts[label_index] += 1
#                         #
#                         #         results.append({
#                         #             'id': q_id,
#                         #             'content': content,
#                         #             'type': type_name,
#                         #             'label': label_name
#                         #         })
#                         #     else:
#                         #         results.append({
#                         #             'id': q_id,
#                         #             'content': content,
#                         #             'type': type_name,
#                         #             'label': ""
#                         #         })
#
#
#
#             except Exception as e:
#                 print(f"处理失败：{q_id}，**{e}")
#
#     # 保存结果到输出文件
#     with open(output_file, 'w', encoding='utf-8') as f:
#         json.dump(results, f, ensure_ascii=False, indent=2)
#         print(len(results))
#         print(label_counts)
#         print(type_counts)
#
# if __name__ == "__main__":
#     # 直接指定输入和输出文件名
#     input_file = '资源教育-语言学习_576_doubao-1.5-pro-32k-250115_周依凡_1753090991559_sci_crawl_13type.json'
#     output_file = '1.5_pro_13_cls_0722_under.json'
#
#     process_data(input_file, output_file)
#
# # 原始数据merged_datas_round1_0.95.json 227470
# # 清洗后json文件中题目的数量
# # 1.5_pro_13_cls_0722_under.json 非超纲问题 133688
# # [6266, 6847, 12060, 1770, 21729, 20252, 9376, 6732, 7757, 7280, 681, 5890, 86, 26962] 非超纲问题的所属核心概念统计
# # [133752, 4207, 2689, 1496, 622, 82407]
# # 1.5_pro_13_cls_0722_all.json 所有问题 224642
# # [15654, 14120, 19872, 4724, 28222, 32628, 9986, 11838, 22066, 9875, 878, 8356, 87, 37338] 属于正常或超纲问题的所属核心概念统计
# # [133483, 4199, 2683, 1495, 621, 82241]
# # '正常': 0,
# # '存在科学错误': 1,
# # '意图不明确': 2,
# # '指代不清淅': 3,
# # '涉及多个提问': 4,
# # '超纲问题': 5
# #
# # '物质的结构与性质': 0,
# # '物质的变化与化学反应': 1,
# # '物质的运动与相互作用': 2,
# # '能的转化与能量守恒': 3,
# # '生命系统的构成层次': 4,
# # '生物体的稳态与调节': 5,
# # '生物与环境的相互关系': 6,
# # '生命的延续与进化': 7,
# # '宇宙中的地球': 8,
# # '地球系统': 9,
# # '人类活动与环境': 10,
# # '技术、工程与社会': 11,
# # '工程设计与物化': 12,
# # '其他': 13,










# fewshot1_input = "什么是热对流"
# fewshot1_output={
#   "核心科学原理": "热对流是液体/气体通过自身流动传递热量的方式。",
#   "实验设计": {
#     "实验": "观察实验 (材料：透明杯 + 色素 + 热水):##1. 杯底注入冷水，轻轻加入一滴热水 (染红色)##2. 观察红色热水像小火箭一样向上窜！",
#     "安全提示": "用温水代替开水防烫伤！"
#   },
#   "居里夫人": {
#     "关键现象解释": "比如煮开水时热水上升、冷水下降形成的循环。",
#     "趣味延伸": {
#       "小行动": "",
#       "冷知识": "煮火锅时，红油锅底比清汤先沸腾 —— 因为油层像“保温毯”锁住热量，形成上下温差更大的对流，加速加热！"
#     }
#   },
#   "达尔文": {
#     "关键现象解释": "比如秋天落叶经常在墙角打转，是因为太阳晒热地面热气上升，冷空气从树荫下来，形成小旋风。",
#     "趣味延伸": {
#       "小行动": "",
#       "冷知识": "变色龙晒太阳时，会故意把深色皮肤朝向来升温更快，深色吸收更多热量，体内血液加速对流循环。"
#     }
#   }
# }
# fewshot2_input = "恐龙灭绝的原因是什么？"
# fewshot2_output={
#   "核心科学原理": "目前最被科学家认可的理论是 “小行星撞击说” -- 约6600万年前，一颗直径 10 公里的小行星撞击地球，引发超级火山喷发、全球气候剧变。",
#   "实验设计": {
#     "实验": "",
#     "安全提示": ""
#   },
#   "居里夫人": {
#     "关键现象解释": "导致气候灾难，撞击扬起的尘埃遮蔽阳光数年，导致地表温度骤降，同时酸雨破坏生态环境。",
#     "趣味延伸": {
#       "小行动": "做个”能源小侦探“：观察家里有哪些电器在待机状态，比如电视、充电器，和家人讨论如何拔掉不用的插头。",
#       "冷知识": "你知道吗？小行星撞地球时，冲击波比音速还快，恐龙可能还没听到声音就被气浪掀飞了。"
#     }
#   },
#   "达尔文": {
#     "关键现象解释": "植物因光照不足大量死亡，植食性恐龙饿死，肉食性恐龙随之灭绝，而小型哺乳动物靠杂食和耐寒特性幸存。",
#     "趣味延伸": {
#       "小行动": "请选择一种你喜欢的动物或植物，为它设计一个”安全屋“。设计时，可以思考动物或植物适合的生存环境以及保护方法。",
#       "冷知识": "你知道吗？蟑螂比恐龙活得好，恐灭绝了，但蟑螂靠吃枯叶、腐木甚至恐龙的粪便活了下来，它们能忍受辐射、高温和饥饿，连小行星撞击都没消灭它们。"
#     }
#   }
# }

# 爬取现象解释，实验等。

#
# merged_concept = ""
# for i in range(1, 14):
#     var_name = f"concept{i}"
#     # 使用eval获取变量的值
#     merged_concept += eval(var_name) + "\n"
# # print(merged_concept)
# import json
# import os
# # 打开输出文件
# with open("sci_exp0722.json", 'w', encoding='utf-8') as outfile:
#     with open("1.5_pro_13_cls_0722_under.json", 'r', encoding='utf-8') as file:
#         all_data = json.load(file)
#         for each_date in all_data:
#             # 构建prompt模板，替换当前类别的concept
#             concept = each_date["label"]
#             prompt = f"""# 任务描述
# 你是一位科普专家，下面是一道属于核心概念【{concept}】的科学问题，你需要首先分析并生成出这道科学问题的通用核心科学原理、实验设计，然后分别站在居里夫人（物理学家）和达尔文（生物学家）两位科学家的角度，分析并生成出这道科学问题的关键现象解释和趣味延伸。
# 核心科学原理是对科学问题中科学本质的概括，生成数量为1；关键现象解释是对核心科学原理的进一步解释，要分别站在居里夫人（物理学）和达尔文（生物学）的角度来解释科学问题中的核心科学原理，内容不要偏离科学问题本身的意图，数量为2；实验设计指的是为小学生设计合理且浅显易懂的实验，应包含实验（实验材料、实验步骤）及安全提示，不同步骤之间要用##隔开，一定要保证实验执行是简单的、材料是常见的、能够让小学生直接进行操作的，切忌在实验中包含危险或潜在危险的操作。若该科学问题不适合设计实验，则实验和安全提示字段为空即可，因此实验设计的生成数量最多为1；趣味延伸分为小行动和冷知识，两者均要与科学问题是相关的。小行动指的是通过科学问题延伸出来的一些具有教育意义且容易让小学生进行的行动，冷知识指的是通过科学问题拓展出的一些相关的冷门但不超纲的知识，需要站在两位科学家的角度上分别生成一个，若对于当前科学问题不适合生成小行动或冷知识，则对应字段为空即可，因此小行动和冷知识的总共数量最多为4。
# 你的输出内容需要参考【学科核心概念定义】中的信息，这是《科学课程标准》中定义的不同年级的学生应该要掌握的知识。你生成的所有内容是面向小学生的，因此不能出现科学错误，且不能生成除学科核心概念的内容要求以外的超纲知识或概念。
# 请参考样例，以json形式直接输出，并且严格按照样例输出格式，不要输出其他内容！
#
# # 学科核心概念定义
# {merged_concept}
#
# # 样例1
# ## 问题
# {fewshot1_input}
# ## 输出
# {fewshot1_output}
#
# # 样例2
# ## 问题
# {fewshot2_input}
# ## 输出
# {fewshot2_output}
#
# # 当前输入
# ## 问题
# {each_date['content']}
#
# # 任务重述
# 请参考样例，并以json形式输出对当前科学问题的分析。
# """
#             # 构建输出数据（保留原id，若需唯一id可改为全局自增）
#             output_data = {
#                 "id": each_date['id'],
#                 "query": prompt
#             }
#             # 写入文件（每行一个json对象）
#             outfile.write(json.dumps(output_data, ensure_ascii=False) + '\n')
#     print(prompt)


fewshot_input_1 = {
        "label": "正常",
        "reason": "",
        "output": {
            "question": "运动是什么？",
            "analysis": {
                "核心科学原理": "运动是物体位置随时间的变化，包括直线运动、曲线运动等多种形式。",
                "实验设计": {
                    "实验": "观察运动实验 (材料：小车 + 轨道):\n1. 将小车放在轨道一端。\n2. 轻轻推动小车，让它在轨道上运动。\n3. 观察小车的运动路径。",
                    "安全提示": "推动小车时不要用力过猛，防止小车滑落伤人。"
                },
                "居里夫人": {
                    "关键现象解释": "就像我们推动一个小球，小球从一个位置移动到另一个位置，这就是小球在做运动。",
                    "趣味延伸": {
                        "小行动": "在操场上观察跑步的同学，看看他们是直线运动还是曲线运动。",
                        "冷知识": "地球也在不停地运动，它不仅自转，还围绕太阳公转。"
                    }
                },
                "达尔文": {
                    "关键现象解释": "比如动物在奔跑、飞翔，它们的位置在不断改变，这就是动物的运动。",
                    "趣味延伸": {
                        "小行动": "观察蚂蚁搬家，看看蚂蚁是怎样运动的。",
                        "冷知识": "有些植物也会运动，比如向日葵会随着太阳转动。"
                    }
                }
            }
        },
        "id": "2362",
        "type": "物质的运动与相互作用"
    }
fewshot_output_1 ={
    "label": "正常",
    "reason": "",
    "output": {
        "question": "运动是什么？",
        "analysis": {
            "核心科学原理": "运动是物体位置随时间的变化，包括直线运动、曲线运动等多种形式。",
            "实验设计": {
                "实验": "观察运动实验 (材料：小车 + 轨道):\n1. 将小车放在轨道一端。\n2. 轻轻推动小车，让它在轨道上运动。\n3. 观察小车的运动路径。",
                "安全提示": "推动小车时不要用力过猛，防止小车滑落伤人。"
            }
        },
        "居里夫人": {
            "关键现象解释": "就像我们推动一个小球，小球从一个位置移动到另一个位置，这就是小球在做运动。",
            "趣味延伸": {
                "小行动": "在操场上观察跑步的同学，看看他们是直线运动还是曲线运动。",
                "冷知识": "地球也在不停地运动，它不仅自转，还围绕太阳公转。"
            }
        },
        "达尔文": {
            "关键现象解释": "比如动物在奔跑、飞翔，它们的位置在不断改变，这就是动物的运动。",
            "趣味延伸": {
                "小行动": "观察蚂蚁搬家，看看蚂蚁是怎样运动的。",
                "冷知识": "有些植物也会运动，比如向日葵会随着太阳转动。"
            }
        }
    },
    "answer": {
        "居里夫人_小行动": "简单来说，运动就是物体的位置随着时间发生了改变，它其实无处不在。就像我们轻轻推动一个小球，能清晰看到它从原来的位置滚动到另一个新的位置。我们可以自己动手做一个更有趣的小实验来感受：在轨道上放一辆小车，轻轻给它一个力（注意安全！），然后仔细观察它滑动的完整路径。课后你还可以去操场上仔细观察跑步的同学，思考他们身体的移动方式，以及运动轨迹是直线还是曲线。",
        "居里夫人_冷知识": "科学上讲，运动是物体位置随时间的变化，从微小的粒子到宏伟的星球都在不停运动。就像推动一个小球，它会从一个地方滚到另一个地方，非常直观。你也可以自己动手，在轨道上轻轻推动小车来观察这个过程（注意安全哦！）。而一个有趣的冷知识是，我们脚下看似静止的地球，其实也一直在高速运动，它不仅自己旋转，还围绕着太阳不知疲倦地奔跑呢！",
        "达尔文_小行动": "运动就是物体位置随时间的变化，这在自然界中随处可见。比如，猎豹在草原上飞速奔跑，鸟儿在天空中自由飞翔，它们的位置都在不断改变，展现了生命的力量。我们可以用小车和轨道做一个安全的模拟实验，轻轻推动小车，观察它的移动路径。做完实验，你再去操场上观察一下跑步的同学，他们的运动方式和动物的奔跑，是不是有许多奇妙的共同点呢？",
        "达尔文_冷知识": "从科学角度看，运动是物体位置随时间的变化，是宇宙中的普遍规律。自然界中，动物的奔跑和飞翔就是生动的例子，充满了节奏与美感。我们也可以用小车和轨道模拟这个过程（注意安全！），观察物体是如何运动的。不仅如此，一个更宏大的运动正在发生：我们赖以生存的地球，其实也是一个运动高手，它正带着我们不停地自转，同时还围绕着太阳公转。"
    },
    "id": "2362",
    "type": "物质的运动与相互作用"
}
fewshot_input_2 =     {
        "label": "正常",
        "reason": "",
        "output": {
            "question": "为什么天空会出现彩虹？",
            "analysis": {
                "核心科学原理": "太阳光中包含不同颜色的光，当太阳光穿过三棱镜或类似三棱镜的介质（如小水滴）时，不同颜色的光会发生不同程度的折射，从而形成彩色光带，即彩虹。",
                "实验设计": {
                    "实验": "模拟彩虹实验 (材料：喷壶 + 阳光充足的地方):\n1. 背对着太阳站立。\n2. 用喷壶朝空中喷水雾。\n3. 观察在水雾中是否出现类似彩虹的彩色光带。",
                    "安全提示": "注意不要将水喷到眼睛里，选择合适的场地避免滑倒。"
                },
                "居里夫人": {
                    "关键现象解释": "雨后，空气中悬浮着大量小水滴，这些小水滴就像一个个三棱镜，太阳光进入水滴后，不同颜色的光被折射出来，就形成了彩虹。",
                    "趣味延伸": {
                        "小行动": "在有阳光的日子，用喷壶在家里的院子或阳台上制造小彩虹，和小伙伴一起观察。",
                        "冷知识": "在飞机上有时能看到完整的圆形彩虹，因为从飞机上看，没有地面的阻挡，彩虹就呈现出完整的圆形。"
                    }
                },
                "达尔文": {
                    "关键现象解释": "彩虹的出现和大气中的水汽有关，在一些潮湿的森林里，偶尔也能看到彩虹，这是因为森林中的水汽和阳光相互作用产生的。",
                    "趣味延伸": {
                        "小行动": "寻找大自然中可能出现彩虹的地方，如瀑布附近，去观察和记录彩虹的样子。",
                        "冷知识": "在古代，人们认为彩虹是神的桥梁，连接着天地，有着美好的寓意。"
                    }
                }
            }
        },
        "id": "10055",
        "type": "物质的运动与相互作用"
    }
fewshot_output_2 ={
    "label": "正常",
    "reason": "",
    "output": {
        "question": "为什么天空会出现彩虹？",
        "analysis": {
            "核心科学原理": "太阳光中包含不同颜色的光，当太阳光穿过三棱镜或类似三棱镜的介质（如小水滴）时，不同颜色的光会发生不同程度的折射，从而形成彩色光带，即彩虹。",
            "实验设计": {
                "实验": "模拟彩虹实验 (材料：喷壶 + 阳光充足的地方):\n1. 背对着太阳站立。\n2. 用喷壶朝空中喷水雾。\n3. 观察在水雾中是否出现类似彩虹的彩色光带。",
                "安全提示": "注意不要将水喷到眼睛里，选择合适的场地避免滑倒。"
            }
        },
        "居里夫人": {
            "关键现象解释": "雨后，空气中悬浮着大量小水滴，这些小水滴就像一个个三棱镜，太阳光进入水滴后，不同颜色的光被折射出来，就形成了彩虹。",
            "趣味延伸": {
                "小行动": "在有阳光的日子，用喷壶在家里的院子或阳台上制造小彩虹，和小伙伴一起观察。",
                "冷知识": "在飞机上有时能看到完整的圆形彩虹，因为从飞机上看，没有地面的阻挡，彩虹就呈现出完整的圆形。"
            }
        },
        "达尔文": {
            "关键现象解释": "彩虹的出现和大气中的水汽有关，在一些潮湿的森林里，偶尔也能看到彩虹，这是因为森林中的水汽和阳光相互作用产生的。",
            "趣味延伸": {
                "小行动": "寻找大自然中可能出现彩虹的地方，如瀑布附近，去观察和记录彩虹的样子。",
                "冷知识": "在古代，人们认为彩虹是神的桥梁，连接着天地，有着美好的寓意。"
            }
        }
    },
    "answer": {
        "居里夫人_小行动": "天空出现彩虹，是因为太阳光被空气中的小水滴折射，分解成了彩色的光带，就像光线穿过三棱镜一样。雨后，空气中悬浮的大量小水滴就扮演了三棱镜的角色。想亲眼看看吗？我们可以自己动手模拟：背对着太阳，用喷壶朝空中喷出水雾（注意安全！），就能制造出属于你自己的迷你彩虹，快和小伙伴一起试试吧。",
        "居里夫人_冷知识": "彩虹是太阳光穿过空气中小水滴时发生的折射现象，这些小水滴如同无数个微小的三棱镜，将阳光分解成七色光芒。我们可以背对太阳喷洒水雾来模拟这个过程（注意安全！），亲手制造一道彩虹。更有趣的是，我们平时看到的彩虹都是弧形，但如果从飞机上向下看，就有机会看到一整个圆形的彩虹，因为它不再被地面遮挡。",
        "达尔文_小行动": "彩虹是阳光穿过空气中的水汽时，因折射而形成的彩色光带。这种现象不仅在雨后出现，在一些水汽充沛的潮湿森林里也能见到。我们可以做一个简单的模拟实验：在阳光下，背对太阳喷洒水雾（注意安全！），观察光的色散。如果你想看更壮观的天然彩虹，可以去瀑布附近等水雾弥漫的地方寻找，并用相机记录下这美丽的瞬间。",
        "达尔文_冷知识": "彩虹的出现，是阳光和大气中水汽相互作用的结果，当光线穿过这些小水滴介质时就会发生折射，形成彩色光带。你知道吗？在科学揭示原理之前，古人无法解释彩虹的成因，常常认为它是连接天地的神之桥梁，赋予了它许多浪漫而美好的神话寓意。我们也可以用喷壶制造水雾来模拟这个奇妙的现象（注意安全！）。"
    },
    "id": "10055",
    "type": "物质的运动与相互作用"
}
fewshot_input_3 = {
    "label": "正常",
    "reason": "",
    "output": {
        "question": "为什么世界上会有人。",
        "analysis": {
            "核心科学原理": "人类是生物进化的结果。在漫长的时间里，生物不断适应环境变化，经过遗传、变异和自然选择等过程，逐渐从简单到复杂、从低级到高级进化，最终演化出了人类。",
            "实验设计": {
                "实验": "",
                "安全提示": ""
            },
            "居里夫人": {
                "关键现象解释": "就像物质在不同的物理条件下会发生变化一样，地球上的生物在不同的环境条件下，其身体结构和特征也会慢慢改变。经过很长时间的变化和积累，就有了人类。",
                "趣味延伸": {
                    "小行动": "",
                    "冷知识": "人类的大脑在进化过程中变得越来越大，现在人类大脑的重量大约占身体重量的2%，但却消耗了身体约20%的能量。"
                }
            },
            "达尔文": {
                "关键现象解释": "在远古时期，地球上生活着很多种类的生物。一些古猿因为环境变化，从树上下到地面生活。为了适应新环境，它们逐渐学会了直立行走、使用工具等，经过一代又一代的演变，就进化成了人类。",
                "趣味延伸": {
                    "小行动": "和小伙伴一起收集不同时期人类化石的图片，比较它们的形态差异。",
                    "冷知识": "在人类进化过程中，曾经出现过很多不同的人种，比如尼安德特人，但只有智人这一人种存活了下来。"
                }
            }
        }
    },
    "id": "1015",
    "type": "生命的延续与进化"
}
fewshot_output_3 = {
    "label": "正常",
    "reason": "",
    "output": {
        "question": "为什么世界上会有人。",
        "analysis": {
            "核心科学原理": "人类是生物进化的结果。在漫长的时间里，生物不断适应环境变化，经过遗传、变异和自然选择等过程，逐渐从简单到复杂、从低级到高级进化，最终演化出了人类。",
            "实验设计": {
                "实验": "",
                "安全提示": ""
            },
            "居里夫人": {
                "关键现象解释": "就像物质在不同的物理条件下会发生变化一样，地球上的生物在不同的环境条件下，其身体结构和特征也会慢慢改变。经过很长时间的变化和积累，就有了人类。",
                "趣味延伸": {
                    "小行动": "",
                    "冷知识": "人类的大脑在进化过程中变得越来越大，现在人类大脑的重量大约占身体重量的2%，但却消耗了身体约20%的能量。"
                }
            },
            "达尔文": {
                "关键现象解释": "在远古时期，地球上生活着很多种类的生物。一些古猿因为环境变化，从树上下到地面生活。为了适应新环境，它们逐渐学会了直立行走、使用工具等，经过一代又一代的演变，就进化成了人类。",
                "趣味延伸": {
                    "小行动": "和小伙伴一起收集不同时期人类化石的图片，比较它们的形态差异。",
                    "冷知识": "在人类进化过程中，曾经出现过很多不同的人种，比如尼安德特人，但只有智人这一人种存活了下来。"
                }
            }
        }
    },
    "answer": {
        "居里夫人_小行动": "",
        "居里夫人_冷知识": "人类的出现是生物进化的奇妙结果。就像物质在不同物理条件下会发生变化，地球生物也在漫长的时间里，为适应环境而慢慢改变自身。经过无数代的积累和演变，最终才有了我们人类。一个有趣的知识是：人类的大脑在进化中变得越来越大，虽然只占体重的2%，却要消耗身体将近20%的能量，是个“高能耗”的器官呢！",
        "达尔文_小行动": "人类是生物长期进化的产物。很久以前，部分古猿因环境变化从树上到地面生活，在适应过程中，它们逐渐学会直立行走、使用工具，经过代代相传的演变，最终进化成了人类。想更直观地感受这个过程吗？你可以和小伙伴一起，收集不同时期人类祖先的化石图片，仔细比较一下他们的头骨和体型有什么奇妙的差异。",
        "达尔文_冷知识": "人类的诞生源于伟大的生物进化。在远古时代，一些古猿为了适应变化的环境，从树栖转向地面生活，并逐渐学会直立行走与使用工具，最终演变成了人类。你知道吗？在漫长的进化史中，地球上曾出现过多个“兄弟”人种，比如强壮的尼安德特人。但经过自然选择的考验，最终只有我们智人这一支延续了下来。"
    },
    "id": "1015",
    "type": "生命的延续与进化"
}
fewshot_input_4 =     {
        "label": "正常",
        "reason": "",
        "output": {
            "question": "水银是什么？",
            "analysis": {
                "核心科学原理": "水银是一种金属，也叫汞，在常温常压下是液态的，具有特殊的物理性质。",
                "实验设计": {
                    "实验": "",
                    "安全提示": ""
                },
                "居里夫人": {
                    "关键现象解释": "水银在一些温度计里能看到，它会随着温度变化而热胀冷缩，显示出不同的刻度。",
                    "趣味延伸": {
                        "小行动": "",
                        "冷知识": "水银的密度很大，比很多常见金属都重，一升水银的重量超过13千克呢！"
                    }
                },
                "达尔文": {
                    "关键现象解释": "在自然界中，如果有一些含汞的矿石被风化，水银可能会慢慢渗出到周围环境中。",
                    "趣味延伸": {
                        "小行动": "",
                        "冷知识": "有些鱼体内可能会积累水银，所以小朋友不能吃太多大型的海鱼哦。"
                    }
                }
            }
        },
        "id": "10054",
        "type": "物质的结构与性质"
    }
fewshot_output_4 = {
    "label": "正常",
    "reason": "",
    "output": {
        "question": "水银是什么？",
        "analysis": {
            "核心科学原理": "水银是一种金属，也叫汞，在常温常压下是液态的，具有特殊的物理性质。",
            "实验设计": {
                "实验": "",
                "安全提示": ""
            },
            "居里夫人": {
                "关键现象解释": "水银在一些温度计里能看到，它会随着温度变化而热胀冷缩，显示出不同的刻度。",
                "趣味延伸": {
                    "小行动": "",
                    "冷知识": "水银的密度很大，比很多常见金属都重，一升水银的重量超过13千克呢！"
                }
            },
            "达尔文": {
                "关键现象解释": "在自然界中，如果有一些含汞的矿石被风化，水银可能会慢慢渗出到周围环境中。",
                "趣味延伸": {
                    "小行动": "",
                    "冷知识": "有些鱼体内可能会积累水银，所以小朋友不能吃太多大型的海鱼哦。"
                }
            }
        }
    },
    "answer": {
        "居里夫人_小行动": "",
        "居里夫人_冷知识": "水银，科学上叫汞，是一种非常特别的金属，它在常温下是银白色的液体。你可能在一些老式温度计里见过它，它会随着温度热胀冷缩，从而指示出准确的读数。这里有个关于它的冷知识：水银的密度非常大，是水的13倍多！这意味着，一小瓶看起来和水差不多的水银，拿在手里却会感觉异常沉重，非常奇妙。",
        "达尔文_小行动": "",
        "达尔文_冷知识": "水银是一种在常温下呈现液态的特殊金属，也叫汞。在自然界中，它有时会从一些含汞的矿石中慢慢渗出，进入到周围的环境里。因此，它可能会通过食物链进行富集，比如在一些大型海鱼的体内积累。所以，为了身体健康，科学家们会提醒我们，尤其是小朋友，不能一次性吃太多像金枪鱼这样的大型深海鱼哦。"
    },
    "id": "1306",
    "type": "我们周围的物质"
}



f"""
#任务描述
你是一位顶级的少儿科普内容专家和专业的科学知识内容整合与精炼专家。尤其擅长将复杂、抽象的科学知识点，通过生动有趣、通俗易懂的方式，转化为适合青少年和充满好奇心的学习者阅读的科普解说。你的文字富有启发性，总能点燃读者的求知欲。你的任务是根据所提供的JSON数据，遵循四种不同的内容组合规则，生成四段独立、精简且通俗易懂的科普回答。

#核心任务:
1.  **输入格式**: 你将收到一个包含特定科学问题“question”、原理分析“analysis”、多角度解读等字段的JSON对象。
2.  **输出要求**: 最终输出必须是一个完整的JSON对象。你必须严格按照下面的“四种答案配方”生成四个答案。在原始输入JSON的结构基础上，新增一个键名为`answer`的字段。输出结果必须清晰地在"answer"字段中标记包含"居里夫人_小行动"、"居里夫人_冷知识"、"达尔文_小行动"、"达尔文_冷知识"字段
3.  **内容来源**: 每个答案都必须融合相应配方中指定的所有字段内容，不得遗漏。
4.  **语言风格**: 答案应保持科普风格，生动有趣，易于理解。

#四种答案配方:
你必须生成四个答案，每个答案的键名和其内容来源配方严格对应如下：
1.  `"居里夫人_小行动":`
    * **内容来源:** `analysis.核心科学原理` + `analysis.实验设计` + `居里夫人.关键现象解释` + `居里夫人.趣味延伸.小行动`

2.  `"居里夫人_冷知识":`
    * **内容来源:** `analysis.核心科学原理` + `analysis.实验设计` + `居里夫人.关键现象解释` + `居里夫人.趣味延伸.冷知识`

3.  `"达尔文_小行动":`
    * **内容来源:** `analysis.核心科学原理` + `analysis.实验设计` + `达尔文.关键现象解释` + `达尔文.趣味延伸.小行动`

4.  `"达尔文_冷知识":`
    * **内容来源:** `analysis.核心科学原理` + `analysis.实验设计` + `达尔文.关键现象解释` + `达尔文.趣味延伸.冷知识`

# 关键约束条件 (必须严格遵守)

1.  **空值处理规则 (最高优先级)**: 在为某个答案（如 “居里夫人_小行动”）生成内容**之前**，必须检查其配方中对应的 `趣味延伸` 部分（如 `居里夫人.趣味延伸.小行动`）是否为**空字符串 `""`**。
    * **如果该字段为空**，则最终 `answer` 对象中对应的键（“居里夫人_小行动”）的值也**必须是空字符串 `""`**，并跳过该答案的文本生成。
    * 此规则对所有四个答案配方均适用。
2.  **内容完整性**: 必须融合配方中指定的所有信息模块，不得随意增减或遗漏。
3.  **移除问题**: 生成的所有答案文本中，**绝对不能包含**原始的`question`字段内容。答案应直接切入解释。
4.  **字数控制**: 每段答案的最终文本长度**必须严格控制在120至130个字符之间（包含所有中英文标点符号）**。
5.  **语言风格**:
    * **通俗生动**: 仿佛在对一个聪明的孩子讲解，避免使用生僻术语。
    * **多用比喻**: 善于运用生活中的常见事物进行类比，帮助理解。
    * **启发互动**: 语言富有启发性，鼓励读者亲身观察或动手尝试。

---
#样例1_完整
##输入
{fewshot_input_1}
##输出
{fewshot_output_1}

#样例2_完整
##输入
{fewshot_input_2}
##输出
{fewshot_output_2}

#样例3_缺值
##输入
{fewshot_input_3}
##输出
{fewshot_output_3}

#样例3_缺值
##输入
{fewshot_input_4}
##输出
{fewshot_output_4}

**[输入JSON数据]:**

"""

