import json


def extract_answer(input_path, output_path):
    all_data = []
    with open(input_path, 'r', encoding='utf-8') as f:
        for line in f.readlines():
            data = json.loads(line)
            all_data.append(data)
    # print(all_data)

    extracted_data = []
    for data in all_data:
        try:
            # 检查answer字段是否为空或None
            if not data.get('answer') or data['answer'].strip() == '':
                print(f"跳过空的answer字段，记录ID: {data.get('id', 'unknown')}")
                continue

            # 解析answer字段中的JSON字符串
            answer_json = json.loads(data['answer'])

            # 提取所需字段
            extracted_record = {
                "id": data["id"],
                "question": answer_json["question"],
                "answer": answer_json["dialogue_turns"]
            }
            extracted_data.append(extracted_record)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误，记录ID: {data.get('id', 'unknown')}, 错误: {e}")
            continue
        except KeyError as e:
            print(f"缺少必要字段，记录ID: {data.get('id', 'unknown')}, 缺少字段: {e}")
            continue

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(extracted_data, f, ensure_ascii=False, indent=4)


if __name__ == '__main__':
    input_path = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\data_v1\资源教育-语言学习_889_doubao-1.5-pro-32k-250115_周依凡_1753799559034_data_MultiTurnDialogue.json'
    output_path = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\data_v1\extracted_answer.json'
    extract_answer(input_path, output_path)