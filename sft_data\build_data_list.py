import json

def load_input_data(input_data_path):
    with open(input_data_path, 'r', encoding='utf-8') as f:
        all_data = json.load(f)
    return all_data

def output_data(output_data_path, data_list):
    with open(output_data_path, 'w', encoding='utf-8') as f:
        f.write(json.dumps(data_list, ensure_ascii=False, indent=4))

def build_data_list(input_data_path, output_data_path):
    all_data = load_input_data(input_data_path)
    data_list = []
    for data in all_data:

        answer_type = list(data.keys())[0]
        answer = data[answer_type]
        role, type = answer_type.split('_')
        data_question = data['input']['question']
        data_analysis = data['input']['analysis']
        if data_analysis['趣味延伸'][role][type] == "":
            continue
        else:
            data_list_item = {
                answer_type: [
                    {
                        'query': data['input']['question'],
                        'input': {
                            'question': data_question,
                            'analysis': {
                                '核心科学原理': data_analysis['核心科学原理'],
                                '关键现象解释': data_analysis['关键现象解释'][role],
                                '实验设计': data_analysis['实验设计'],
                                '趣味延伸': data_analysis['趣味延伸'][role][type],
                            }
                        },
                        'output': answer
                    }
                ]
            }
            data_list.append(data_list_item)


    data_count = 1
    data_map = {}
    for data in data_list:
        for key, value in data.items():
            query_count = value[0]['query']
            if query_count not in data_map:
                data_map[query_count] = data_count
                data_count += 1
    data_total_count = len(data_map)
    print('data_total_count:', data_total_count)
    output_data(output_data_path, data_list)


if __name__ == '__main__':
    input_data_path = r'D:\ProJects\kexue\crawl_data\0729\03\sci_answer.json'
    output_data_path = r'data_list.json'
    build_data_list(input_data_path, output_data_path)