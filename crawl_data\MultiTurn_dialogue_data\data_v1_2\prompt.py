import json

concept1= """【物质的结构与性质】
学段：1-2年级
- **学习内容1.1：物质具有一定的特性与功能**
  - 内容要求：
    - ①观察并描述物体的轻重、薄厚、颜色、表面粗糙程度、形状等外部特征，能根据物体的外部特征对其进行简单分类。
    - ②识别生活中常见的材料。
- **学习内容1.2：空气与水是重要的物质**
  - 内容要求：
    - ③认识空气是无色、无味的气体。
    - ④观察并描述水的颜色、状态、气味等特征。
- **学习内容1.3：金属及合金是重要的材料**
  - 内容要求：
    - ⑤举例说出生活中常见的金属，知道金属是常见的材料。

学段：3-4年级
- **学习内容1.1：物质具有一定的特性与功能**
  - 内容要求：
    - ①能使用简单的仪器测量一些物体的长度、质量、体积、温度等常见特征，并使用恰当的计量单位进行记录。
    - ②能根据物体的特征或材料的性质将两种混合在一起的物体分离开来，如分离沙和糖、铁屑和木屑等。
    - ③描述某些材料的透光性、导电性，说出它们的主要用途。
- **学习内容1.2：空气与水是重要的物质**
  - 内容要求：
    - ④说明空气有质量并占有一定的空间，空气会充满各处。
    - ⑤观察并描述空气受热上升的现象。
    - ⑥知道风是一种空气流动的现象，列举生活中常见的形成风的一些方法。
    - ⑦说出冰、水、水蒸气在状态和体积等方面的区别，知道三者虽然状态不同，但都是同一种物质。
    - ⑧观察并描述水沸腾或结冰的现象；了解一般状况下，水沸腾和结冰时的温度，知道温度是影响水沸腾和结冰的重要因素。

学段：5-6年级
- **学习内容1.1：物质具有一定的特性与功能**
  - 内容要求：
    - ①观察常见材料在水中的沉浮现象、导热性等，说出它们的主要用途。
- **学习内容1.2：空气与水是重要的物质**
  - 内容要求：
    - ②知道空气是一种混合物，含有氮气、氧气、二氧化碳等气体，空气中的氧气和二氧化碳对生命活动具有重要意义。
    - ③列举日常生活中水的蒸发和水蒸气凝结成水的实例，如晒衣服、雾、玻璃窗上的水珠等。"""
concept2="""【物质的变化与化学反应】
学段：1-2年级
- **学习内容2.2：物质的溶解和溶液**
  - 内容要求：
    - ①知道有些物质能溶解在水中，如食盐和白糖等；有些物质很难溶解在水中，如沙和食用油等。

学段：3-4年级
- **学习内容2.1：物质的三态变化**
  - 内容要求：
    - ①知道固体有确定的形状、体积和质量；液体有确定的体积和质量，液体静止时其表面一般会保持水平；气体有确定的质量，但没有确定的体积和形状。
    - ②描述加热或冷却时常见物质发生的状态变化，如水结冰、冰熔化、水蒸发和水蒸气凝结。
- **学习内容2.2：物质的溶解和溶液**
  - 内容要求：
    - ③描述一定量的不同物质在一定量水中的溶解情况。
    - ④知道是否搅拌和温度高低是影响物质在水中溶解快慢的常见因素。
- **学习内容2.3：物质变化的特征**
  - 内容要求：
    - ⑤知道有些物体的形状或大小发生了变化，如被切成小块、被挤压、被拉伸等，构成物体的物质没有改变。

学段：5-6年级
- **学习内容2.3：物质变化的特征**
  - 内容要求：
    - ①知道有些物体发生了变化，如纸燃烧、铁生锈等，构成物体的物质也发生了改变。"""
concept3="""【物质的运动与相互作用】
学段：1-2年级
- **学习内容3.1：力是改变物体运动状态的原因**
  - 内容要求：
    - ①使用前后左右、东南西北、上下、远近等描述物体所处的位置和方向。
    - ②知道推力和拉力是常见的力，力可以使物体的形状发生改变。
- **学习内容3.2：电磁相互作用**
  - 内容要求：
    - ③列举常用的不同形状的磁铁。
    - ④知道磁铁可以直接或隔着一段距离对铁、镍等材料产生吸引作用。

学段：3-4年级
- **学习内容3.1：力是改变物体运动状态的原因**
  - 内容要求：
    - ①知道可以用相对于另一个物体的方向和距离来描述运动物体在某个时刻的位置。
    - ②知道测量距离和时间的常用方法。
    - ③知道用速度的大小来描述物体运动的快慢，知道自行车、火车、飞机等常用交通工具的大致速度。
    - ④描述生活中常见物体的直线运动、曲线运动等运动方式，比较不同的运动，举例说明各种运动的形式和特征。
- **学习内容3.2：电磁相互作用**
  - 内容要求：
    - ⑤知道磁铁同时存在两个不同的磁极，相同的磁极互相排斥，不同的磁极互相吸引。
    - ⑥知道地球有磁场，指南针中的小磁针可以用来指示南北。
    - ⑦知道电源、导线、用电器和开关是构成电路的必要元件；说明形成电路的条件，切断闭合回路是控制电流的一种方法。
    - ⑧知道有些材料是导体，容易导电；有些材料是绝缘体，不容易导电。
    - ⑨列举电的重要用途，知道雷电、高压电、家庭电路中的交流电会对人体产生伤害，知道安全用电的常识。
- **学习内容3.3：声音与光的传播**
  - 内容要求：
    - ⑩识别来自光源的光（如太阳光、灯光）或来自物体反射的光（如月光）。
    - ⑪描述光被阻挡时形成阻挡物影子的现象。
    - ⑫举例说明声音因物体的振动而产生。
    - ⑬举例说明声音在不同物质中可以向各个方向传播。
    - ⑭知道声音有高低和强弱之分，声音高低和强弱的变化是由于振动的变化引起的；制作能产生不同高低、强弱声音的简易装置。
    - ⑮知道噪声的危害和防治，学会保护听力的常用方法。

学段：5-6年级
- **学习内容3.1：力是改变物体运动状态的原因**
  - 内容要求：
    - ①知道日常生活中常见的摩擦力、弹力、浮力等都是直接施加在物体上的力。
    - ②举例说明给物体施加力可以改变物体运动的快慢，也可以使物体开始或停止运动；学会使用弹簧测力计。
    - ③知道地球上一切物体都受到地球的吸引力，地球不需要接触物体就可以对其施加引力。
- **学习内容3.3：声音与光的传播**
  - 内容要求：
    - ④知道来自光源的光或来自物体的反射光进入眼睛，能使人们看到光源或该物体。
    - ⑤知道光在空气中沿直线传播。
    - ⑥知道光遇到物体会发生反射现象，光的传播方向会发生改变。
    - ⑦描述太阳光穿过三棱镜后形成的彩色光带，知道太阳光中包含不同颜色的光。"""
concept4="""【能的转化与能量守恒】
学段：3-4年级
- **学习内容4.1：能的形式、转移与转化**
  - 内容要求：
    - ①了解生活中各种能的形式，知道运动的物体具有能量。
    - ②描述测量物体温度的方法，知道摄氏度是表示物体冷热程度的常用温度标准单位。
    - ③知道物体一般具有热胀冷缩的性质，知道水结冰时体积会膨胀。

学段：5-6年级
- **学习内容4.1：能的形式、转移与转化**
  - 内容要求：
    - ①知道动能、声能、光能、热能、电能、磁能等都是能的形式，了解这些能的相互转化现象。
    - ②知道简单机械（杠杆、滑轮、轮轴、斜面）及其在生产生活中的应用。
    - ③举例说出生活中常见的热传递现象，知道热从温度高的物体传向温度低的物体，从物体温度高的部分传向温度低的部分。
    - ④举例说明影响热传递的主要因素，列举它们在日常生产生活中的应用。
- **学习内容4.2：能源与可持续发展**
  - 内容要求：
    - ⑤了解太阳能、水能、风能、地热能、化石能等能源。"""
concept5="""【生命系统的构成层次】
学段：1-2年级
- **学习内容5.1：生物具有区别于非生物的特征**
  - 内容要求：
    - ①举例说明动物和植物都是生物。
- **学习内容5.2：地球上存在动物、植物、微生物等不同类型的生物**
  - 内容要求：
    - ②说出生活中常见动物的名称及特征，说出动物的某些共同特征（如都会运动）。
    - ③说出周围常见植物的名称及特征。
- **学习内容5.5：人体由多个系统组成**
  - 内容要求：
    - ④识别人的眼、耳、鼻、舌、皮肤等器官，列举这些器官的功能与保护方法。

学段：3-4年级
- **学习内容5.1：生物具有区别于非生物的特征**
  - 内容要求：
    - ①说出生物与非生物的不同特点，描述生物的特征。
- **学习内容5.2：地球上存在动物、植物、微生物等不同类型的生物**
  - 内容要求：
    - ②根据某些特征，对动物进行分类。
    - ③识别常见的动物类别，描述某一类动物（如昆虫、鱼类、鸟类、哺乳类）的共同特征；列举几种我国的珍稀动物。
    - ④说出植物的某些共同特征；列举当地的植物资源，尤其是与人类生活密切相关的植物。
- **学习内容5.4：生物体具有一定的结构层次**
  - 内容要求：
    - ⑤描述植物一般由根、茎、叶、花、果实和种子构成。
- **学习内容5.5：人体由多个系统组成**
  - 内容要求：
    - ⑥描述人体用于呼吸的器官，列举保护这些器官的方法。
    - ⑦描述人体用于摄取养分的器官，列举保护这些器官的方法。
- **学习内容5.6：生态系统由生物与非生物环境共同组成**
  - 内容要求：
    - ⑧举例说出水、阳光、空气、温度的变化对生物生存的影响。
    - ⑨列举动物依赖植物筑巢或作为庇护所的实例。

学段：5-6年级
- **学习内容5.2：地球上存在动物、植物、微生物等不同类型的生物**
  - 内容要求：
    - ①列举生活中常见的微生物（如酵母菌、霉菌、病毒），举例说出感冒、痢疾等疾病是由微生物引起的。
    - ②根据某些特征，对植物进行分类。
- **学习内容5.3：细胞是生物体结构与生命活动的基本单位**
  - 内容要求：
    - ③初步学会使用显微镜观察细胞，知道细胞是生物体的基本结构单位。
- **学习内容5.5：人体由多个系统组成**
  - 内容要求：
    - ④说出脑是认知、情感、意志和行为的生物学基础，举例说出保护脑健康的主要措施。
- **学习内容5.6：生态系统由生物与非生物环境共同组成**
  - 内容要求：
    - ⑤举例说出常见的栖息地为生物提供光、空气、水、适宜的温度和食物等基本条件。
    - ⑥说出常见动物和植物之间吃与被吃的链状关系。"""
concept6="""【生物体的稳态与调节】
学段：1-2年级
- **学习内容6.1：植物能制造和获取养分来维持自身的生存**
  - 内容要求：
    - ①说出植物的生存和生长需要水、阳光和空气。
- **学习内容6.2：人和动物通过获取其他生物的养分来维持生存**
  - 内容要求：
    - ②举例说出动物可以通过眼、耳、鼻等器官感知环境。

学段：3-4年级
- **学习内容6.1：植物能制造和获取养分来维持自身的生存**
  - 内容要求：
    - ①描述植物的生存和生长需要水、阳光、空气和适宜的温度。
    - ②描述植物的根、茎、叶、花、果实和种子具有帮助植物维持自身生存的相应功能。
- **学习内容6.2：人和动物通过获取其他生物的养分来维持生存**
  - 内容要求：
    - ③举例说出动物通过皮肤、四肢、翼、鳍、鳃等接触和感知环境。
    - ④描述动物维持生命需要空气、水、食物和适宜的温度。

学段：5-6年级
- **学习内容6.1：植物能制造和获取养分来维持自身的生存**
  - 内容要求：
    - ①知道植物可以利用阳光、空气和水分在绿色叶片中制造其生存所需的养分。
- **学习内容6.2：人和动物通过获取其他生物的养分来维持生存**
  - 内容要求：
    - ②知道动物以其他生物为食，动物维持生命需要消耗这些食物而获得能量。
    - ③说出人体生长发育所需的主要营养物质及其消化吸收过程。
- **学习内容6.3：人体通过一定的调节机制保持稳态**
  - 内容要求：
    - ④举例说出人体对某些环境刺激的反应方式和作用，列举保护相关器官的方法。"""
concept7="""【生物与环境的相互关系】
学段：3-4年级
- **学习内容7.1：生物能适应其生存环境**
  - 内容要求：
    - ①举例说出生活在不同环境中的植物的外部形态具有不同的特点，以及这些特点对维持植物生存的作用。
    - ②举例说出动物适应季节变化的方式，说出这些变化对维持动物生存的作用。

学段：5-6年级
- **学习内容7.1：生物能适应其生存环境**
  - 内容要求：
    - ①举例说出动物在气候、食物、空气和水源等环境变化时的行为。
- **学习内容7.3：人的生活习惯影响机体健康**
  - 内容要求：
    - ②列举睡眠、饮食、运动等影响健康的因素，养成良好的生活习惯。
- **学习内容7.4：人体生命安全与生存环境密切相关**
  - 内容要求：
    - ③举例说出重大传染病和突发公共卫生事件对人类安全的威胁。

学段：5-6年级
- **学习内容7.1：生物能适应其生存环境**
  - 内容要求：
    - ①举例说出动物在气候、食物、空气和水源等环境变化时的行为。
- **学习内容7.3：人的生活习惯影响机体健康**
  - 内容要求：
    - ②列举睡眠、饮食、运动等影响健康的因素，养成良好的生活习惯。
- **学习内容7.4：人体生命安全与生存环境密切相关**
  - 内容要求：
    - ③举例说出重大传染病和突发公共卫生事件对人类安全的威胁。"""
concept8="""【生命的延续与进化】
学段：3-4年级
- **学习内容8.1：植物通过多种方式进行繁殖**
  - 内容要求：
    - ①举例说出植物从生到死的生命过程；举例说出植物通常会经历由种子萌发成幼苗，再到开花、结出果实和种子的过程。
    - ②描述有的植物通过产生种子繁殖后代，有的植物通过根、茎、叶等繁殖后代。
    - ③列举动物帮助植物传粉或传播种子的实例。
- **学习内容8.2：不同种类动物具有不同的生殖方式和发育过程**
  - 内容要求：
    - ④举例说出动物从生到死的生命过程。
    - ⑤描述和比较胎生与卵生动物的繁殖方式。

学段：5-6年级
- **学习内容8.3：人的生命是从受精卵开始的**
  - 内容要求：
    - ①认识青春期及其特征，关注青春期保健。
- **学习内容8.5：生物体的遗传信息逐代传递，可发生改变**
  - 内容要求：
    - ②描述和比较植物子代与亲代在形态特征方面的异同。
    - ③描述和比较动物子代与亲代在形态特征方面的异同。
- **学习内容8.6：生物的遗传变异和环境因素的共同作用导致了生物的进化**
  - 内容要求：
    - ④根据化石资料，举例说出已灭绝的生物；描述和比较灭绝生物与当今某些生物的相似之处。"""
concept9="""【宇宙中的地球】
学段：1-2年级
- **学习内容9.2：地球绕地轴自转**
  - 内容要求：
    - ①观察并描述太阳每天在天空中东升西落的位置变化，初步学会根据太阳的位置辨认方向。
- **学习内容9.3：地球围绕太阳公转**
  - 内容要求：
    - ②描述一年中季节变化的现象，举例说出季节变化对动植物和人们生活的影响。
- **学习内容9.4：月球是地球的卫星**
  - 内容要求：
    - ③知道每天观察到的月亮形状是变化的。

学段：3-4年级
- **学习内容9.1：地球是一颗行星**
  - 内容要求：
    - ①知道地球是一个球体，是太阳系中的一颗行星，太阳系有八颗行星。
- **学习内容9.2：地球绕地轴自转**
  - 内容要求：
    - ②观察并描述太阳光照射下物体影长从早到晚的变化情况。
- **学习内容9.4：月球是地球的卫星**
  - 内容要求：
    - ③知道月球是地球的天然卫星；通过望远镜观察或利用图片资料，了解月球表面的概况。

学段：5-6年级
- **学习内容9.2：地球绕地轴自转**
  - 内容要求：
    - ①知道地球的自转轴、自转周期和自转方向，理解昼夜交替和天体东升西落等自然现象与地球的自转有关。
- **学习内容9.3：地球围绕太阳公转**
  - 内容要求：
    - ②知道地球围绕太阳公转的周期和方向，理解四季的形成与地球的公转有关。
    - ③测量正午时物体的影长，说明不同季节正午影长的变化情况。
- **学习内容9.4：月球是地球的卫星**
  - 内容要求：
    - ④知道新月、上弦月、满月、下弦月四种月相，说明月相的变化情况。
- **学习内容9.5：地球所处的宇宙环境**
  - 内容要求：
    - ⑤比较太阳、地球、月球的相对大小，知道太阳是一颗恒星，是太阳系的中心天体，描述太阳系八颗行星在太阳系中的相对位置。
    - ⑥知道宇宙中有很多恒星，通过观察或借助软件识别织女星、牛郎星等亮星，学会利用北极星辨认方向；知道大熊座、猎户座等星座，了解划分星座的意义。
- **学习内容9.6：太空探索拓展了人类对宇宙的认知**
  - 内容要求：
    - ⑦了解人造卫星和载人航天的历史，知道太空环境对人体健康的影响，关注我国航天事业的进展。
    - ⑧了解天文观测和利用航天器探测宇宙的历史，关注我国月球和深空探测事业的进展。"""
concept10="""【地球系统】
学段：1-2年级
- **学习内容10.1：天气和气候**
  - 内容要求：
    - ①知道阴、晴、雨、雪、风等天气现象。
    - ②描述天气变化对动植物和人类生活的影响。
- **学习内容10.3：岩石和土壤**
  - 内容要求：
    - ③知道土壤为众多动植物提供了生存场所。

学段：3-4年级
- **学习内容10.1：天气和气候**
  - 内容要求：
    - ①知道地球表面被大气包围着，大气是运动的；学会使用气温计测量气温，并描述一天中气温的变化。
    - ②学会使用仪器测量和记录气温、风力、风向、降水量等气象数据，并运用测量结果描述天气状况；识别常用的天气符号，理解天气预报用语。
- **学习内容10.2：水循环**
  - 内容要求：
    - ③知道地球表面的海陆分布情况，说出河流、湖泊、海洋、冰川、地下水等主要水体类型。
- **学习内容10.3：岩石和土壤**
  - 内容要求：
    - ④知道土壤的主要成分，观察并描述砂质土、黏质土、壤质土的特点，举例说出它们适宜生长的植物。

学段：5-6年级
- **学习内容10.1：天气和气候**
  - 内容要求：
    - ①知道雨、雪、雾等天气现象的成因。
- **学习内容10.2：水循环**
  - 内容要求：
    - ②知道水在改变地表形态的过程中发挥着重要作用。
- **学习内容10.3：岩石和土壤**
  - 内容要求：
    - ③知道地球表面覆盖着岩石，岩石是由矿物组成的；学会通过观察和使用简单工具，比较不同岩石的颜色、坚硬程度、颗粒粗细等特征。
- **学习内容10.4：地球内部圈层和地壳运动**
  - 内容要求：
    - ④知道地球内部分为地壳、地幔和地核，地壳主要由岩浆岩、沉积岩和变质岩构成，了解化石的形成及科学价值；知道火山喷发和地震是地球内部能量集中释放产生的自然现象。"""
concept11="""【人类活动与环境】
学段：1-2年级
- **学习内容11.3：人类活动对环境的影响**
  - 内容要求：
    - ①举例说出人类的生活与自然环境有关，知道地球是人类与动植物共同的家园。
    - ②知道有些材料可以被回收利用，树立节约资源、保护环境的意识。

学段：3-4年级
- **学习内容11.1：自然资源**
  - 内容要求：
    - ①说出人类利用矿产资源进行工业生产的例子，树立合理利用矿产资源的意识。
    - ②说出人类生活离不开水的例子，树立节约用水的意识。
    - ③知道土壤是农业生产的基础，树立保护土壤资源的意识。
- **学习内容11.3：人类活动对环境的影响**
  - 内容要求：
    - ④有些废旧材料可以被分类和回收。

学段：5-6年级
- **学习内容11.1：自然资源**
  - 内容要求：
    - ①知道海洋为人类生存提供了能源、生物资源、矿产资源等，树立海洋国土意识。
    - ②知道资源可分为可再生资源和不可再生资源；以垃圾分类为例，通过践行垃圾减量与分类回收，树立循环利用资源的意识。
- **学习内容11.2：自然灾害**
  - 内容要求：
    - ③结合实例，知道台风、洪涝、干旱、沙尘暴、泥石流等灾害及其影响，树立自我保护和防灾减灾的意识。
- **学习内容11.3：人类活动对环境的影响**
  - 内容要求：
    - ④正确认识经济发展和生态环境保护的关系，结合实例，说明人类不合理的开发活动对环境的影响，提出保护环境的建议，参与保护环境的行动。
    - ⑤关注野生动物和濒危植物的保护，拒绝濒危动植物及其产品贸易，认识到保护生物多样性的重要性。"""
concept12="""【技术、工程与社会】
学段：1-2年级
- **学习内容12.1：技术与工程创造了人造物，技术的核心是发明，工程的核心是建造**
  - 内容要求：
    - ①知道我们周围的人造物是由人设计并制造出来的，观察和区别身边的自然物和人造物。
    - ②学会使用锤子、安全剪刀、放大镜等简单工具；应用身边的材料和工具，制作简单的手工作品。
- **学习内容12.2：技术与工程改变了人们的生产和生活**
  - 内容要求：
    - ③举例说出周围简单科技产品的结构和功能，知道科技产品给人们生活带来的便利、快捷和舒适。
- **学习内容12.3：科学、技术、工程相互影响与促进**
  - 内容要求：
    - ④初步体验利用工具可以更好地进行观察与测量。

学段：3-4年级
- **学习内容12.1：技术与工程创造了人造物，技术的核心是发明，工程的核心是建造**
  - 内容要求：
    - ①区别生活中常见的天然材料和人造材料，说出中国古代技术与工程方面的典型案例。
    - ②举例说出工具在生产和生活中的应用，知道使用工具可以更加便利、快捷和精确；学会使用常见的工具制作简单作品；拆装简单产品，了解产品的构造和特点。
- **学习内容12.2：技术与工程改变了人们的生产和生活**
  - 内容要求：
    - ③举例说出一些典型的技术（如交通技术、电力技术等）和工程（如高速铁路、发电站等）对人们生活的影响；尝试设计和制作某种产品的简化实物模型，并反映其中的部分科学原理。
- **学习内容12.3：科学、技术、工程相互影响与促进**
  - 内容要求：
    - ④初步说明一些技术产品涉及的科学概念或原理，尝试应用科学原理设计并制作简易装置（如传声器、听诊器等）。

学段：5-6年级
- **学习内容12.1：技术与工程创造了人造物，技术的核心是发明，工程的核心是建造**
  - 内容要求：
    - ①知道技术包括方法、程序和产品等；知道发明的常用方法，举例说出一些典型的发明，知道发明会用到一定的科学原理，很多发明可以在自然界找到原型。
    - ②知道工程以科学和技术为基础，知道工程通常由多个系统组成；知道中国主要的大科学工程。
- **学习内容12.2：技术与工程改变了人们的生产和生活**
  - 内容要求：
    - ③知道技术对提高生产效率或工作效率的影响，举例说明应用适当技术可以提高生产效率或工作效率，应用所学科学原理设计并制作出可以提高效率的作品。
- **学习内容12.3：科学、技术、工程相互影响与促进**
  - 内容要求：
    - ④初步认识技术与工程对科学发展的促进作用，应用仪器设备进行观察并进行记录；举例说明科学发现可以促进新技术发明（如激光的发明）。"""
concept13="""【工程设计与物化】
学段：1-2年级
- **学习内容13.1：工程需要定义和界定**
  - 内容要求：
    - ①通过观察，提出并描述简单的制作问题。
- **学习内容13.2：工程的关键是设计**
  - 内容要求：
    - ②学会使用简单的草图，说出自己的思路。
- **学习内容13.3：工程是设计方案物化的结果**
  - 内容要求：
    - ③学会使用简单的工具，对生活中常见的材料进行简单的加工处理。
    - ④制作简单的实物模型并展示，尝试通过观察发现作品中存在的问题并提出改进方案。

学段：3-4年级
- **学习内容13.1：工程需要定义和界定**
  - 内容要求：
    - ①描述简单的设计问题，包括材料、时间或成本等限制条件。
- **学习内容13.2：工程的关键是设计**
  - 内容要求：
    - ②借助表格、草图、实物模型、戏剧或故事等方式说明自己的设计思路。
    - ③根据需求和限制条件，比较多种可能的解决方案，并初步判断其合理性。
- **学习内容13.3：工程是设计方案物化的结果**
  - 内容要求：
    - ④利用常用工具，对常见材料进行简单加工处理。
    - ⑤知道制作过程应遵循一定的顺序，制作简单的实物模型；尝试发现实物模型的不足，改进并展示。

学段：5-6年级
- **学习内容13.1：工程需要定义和界定**
  - 内容要求：
    - ①定义简单工程问题，包括材料、时间或成本等限制条件，提出验收标准。
- **学习内容13.2：工程的关键是设计**
  - 内容要求：
    - ②利用示意图、影像、文字或实物等多种方式，阐明自己的创意，初步认识设计方案中各影响因素间的关系。
    - ③基于有说服力的论证，认同或质疑某些设计方案，并初步判断其可行性和合理性。
- **学习内容13.3：工程是设计方案物化的结果**
  - 内容要求：
    - ④利用工具制作简单的实物模型，根据实际反馈结果进行改进并展示。"""

CATEGORY_CONCEPT_MAP = {
    "物质的结构与性质": "{{concept1}}",
    "物质的变化与化学反应": "{{concept2}}",
    "物质的运动与相互作用": "{{concept3}}",
    "能的转化与能量守恒": "{{concept4}}",
    "生命系统的构成层次": "{{concept5}}",
    "生物体的稳态与调节": "{{concept6}}",
    "生物与环境的相互关系": "{{concept7}}",
    "生命的延续与进化": "{{concept8}}",
    "宇宙中的地球": "{{concept9}}",
    "地球系统": "{{concept10}}",
    "人类活动与环境": "{{concept11}}",
    "技术、工程与社会": "{{concept12}}",
    "工程设计与物化": "{{concept13}}"
}



# fewshot_output_1 = {
#   "question": "宇航员为什么要带月壤回来研究？",
#   "dialogue_turns": [
#     {
#       "step": "问题识别与澄清",
#       "role": "老师",
#       "cont": "你问得真仔细！月壤是月球表面的“皮肤”，猜猜它摸起来像面粉还是像石头？"
#     },
#     {
#       "step": "问题识别与澄清",
#       "role": "学生",
#       "cont": "像面粉！"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "老师",
#       "cont": "你记得玩沙子吗？干沙子会从指缝流走，湿沙子能捏成团。月壤可能更像哪一种？"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "学生",
#       "cont": "像干沙子！"
#     },
#     {
#       "step": "问题优化",
#       "role": "老师",
#       "cont": "观察得真准！再想想它看起来是黑色还是灰色？像什么食物呢？"
#     },
#     {
#       "step": "问题优化",
#       "role": "学生",
#       "cont": "黑黑的，像芝麻糊！"
#     },
#     {
#       "step": "产出可探究问题",
#       "role": "老师",
#       "cont": "太棒了！现在当小小科学家！把你的以上的发现变成一个可以探究的问题吧！"
#     },
#     {
#       "step": "产出可探究问题",
#       "role": "学生",
#       "cont": "月壤的触觉特征是什么？"
#     },
#     {
#       "step": "对话结束与鼓励",
#       "role": "老师",
#       "cont": "太棒啦！你提出了一个非常好的科学问题！"
#     }
#   ],
#   "dialogue_logic_chain": [
#     {
#       "问答阶段": "问题识别与澄清",
#       "核心问题": "月壤是什么样的？",
#       "核心操作": "限定感官通道（触觉、视觉）"
#     },
#     {
#       "问答阶段": "概念迁移与学段适配",
#       "核心问题": "月壤更像生活中的什么？",
#       "核心操作": "实物类比（沙子）"
#     },
#     {
#       "问答阶段": "问题优化",
#       "核心问题": "如何描述月壤的特征？",
#       "核心操作": "追加感官维度，聚焦核心特征"
#     },
#     {
#       "问答阶段": "产出可探究问题",
#       "核心问题": "月壤的触觉特征是什么？",
#       "核心操作": "引导学生自主总结并提出描述性问题"
#     }
#   ],
# }
# 定义fewshot输出示例，避免f-string中的双引号冲突
fewshot_input_1 = {
    "question": "宇航员为什么要带月壤回来研究？"
}
fewshot_output_1 = '''<JSON>{"step": "问题识别与澄清", "role": "老师", "cont": "你问得真仔细！月壤是月球表面的"皮肤"，猜猜它摸起来像面粉还是像石头？", "display_cont": "限定感官通道（触觉），将抽象问题"为什么"转化为具体的"像什么"."}}</JSON>
<JSON>{"step": "问题识别与澄清", "role": "学生", "cont": "像面粉！", "display_cont": ""}</JSON>
<JSON>{"step": "概念迁移与学段适配", "role": "老师", "cont": "你记得玩沙子吗？干沙子会从指缝流走，湿沙子能捏成团。月壤可能更像哪一种？", "display_cont": "使用生活经验（玩沙子）进行二选一分类，建立新旧知识联系."}</JSON>
<JSON>{"step": "概念迁移与学段适配", "role": "学生", "cont": "像干沙子！", "display_cont": ""}</JSON>
<JSON>{"step": "问题优化", "role": "老师", "cont": "观察得真准！再想想它看起来是黑色还是灰色？像什么食物呢？", "display_cont": "追加感官维度（视觉），丰富对现象的描述。"}</JSON>
<JSON>{"step": "问题优化", "role": "学生", "cont": "黑黑的，像芝麻糊！", "display_cont": ""}</JSON>
<JSON>{"step": "产出可探究问题", "role": "老师", "cont": "太棒了，你已经像个小小科学家一样描述了月壤！现在，你能试着把你最好奇的，关于月壤摸起来感觉的这一点，变成一个问题吗？", "display_cont": "采用自然语言引导，邀请学生自主总结并提出描述性问题，而不是给出问题模板。"}</JSON>
<JSON>{"step": "产出可探究问题", "role": "学生", "cont": "我想知道，月壤的触觉特征是什么？", "display_cont": ""}</JSON>
<JSON>{"step": "对话结束与鼓励", "role": "老师", "cont": "太棒啦！你提出了一个非常好的科学问题！", "display_cont": "对学生自主生成的问题给予高度肯定，强化成就感。"}</JSON>'''

fewshot_input_2 = {
    "question": "永动机是什么"
}
fewshot_output_2 = '''<JSON>{"step": "问题识别与澄清", "role": "老师", "cont": "你主动提出这个问题太棒了！ 能告诉老师你是从哪部动画片里看到永动机的吗？", "display_cont": "兴趣源定位，生活现象对比"}</JSON>
<JSON>{"step": "问题识别与澄清", "role": "学生", "cont": "《超级发明家》...里面的机器一直转...", "display_cont": ""}</JSON>
<JSON>{"step": "问题识别与澄清", "role": "老师", "cont": "你的记忆力真强！ 不过老师很好奇，现实中的电风扇需要插电，永动机最神奇的是不是它不需要任何能量？", "display_cont": "兴趣源定位，生活现象对比"}</JSON>
<JSON>{"step": "问题识别与澄清", "role": "学生", "cont": "嗯...它自己就能动！", "display_cont": ""}</JSON>
<JSON>{"step": "概念迁移与学段适配", "role": "老师", "cont": "你刚才的观察很仔细！我们来看一个真实的装置：水电站。如果水流的大小不一样，水轮的转动会有什么不同吗？", "display_cont": "磁铁小车反例，滑梯能量类比"}</JSON>
<JSON>{"step": "概念迁移与学段适配", "role": "学生", "cont": "水流越大，转得越快！", "display_cont": ""}</JSON>
<JSON>{"step": "问题优化", "role": "老师", "cont": "完美的推断！"水流大小"这个词很棒，但如果我们要做实验，怎样才能精确地改变它呢？", "display_cont": "引导思考可操作变量。将"水流大小"具体化。"}</JSON>
<JSON>{"step": "问题优化", "role": "学生", "cont": "可以从不同的高度倒水？高度越高，水流冲击的力量应该越大。", "display_cont": ""}</JSON>
<JSON>{"step": "问题优化", "role": "老师", "cont": "这个主意太聪明了！为了让实验更科学，我们还需要保证其他条件不变，比如每次倒的水量要一样吗？", "display_cont": "引导思考控制变量（水量）。"}</JSON>
<JSON>{"step": "问题优化", "role": "学生", "cont": "要一样！比如都用200毫升的水。", "display_cont": ""}</JSON>
<JSON>{"step": "产出可探究问题", "role": "老师", "cont": "太了不起了！最后一个问题，我们怎么精确地知道哪个"更快"呢？可以用数字来表示吗？", "display_cont": "引导思考可量化指标（转/秒）。"}</JSON>
<JSON>{"step": "产出可探究问题", "role": "学生", "cont": "可以数一下10秒钟转了多少圈！", "display_cont": ""}</JSON>
<JSON>{"step": "产出可探究问题", "role": "老师", "cont": "祝贺你！你已经像一个真正的工程师一样，设计出了一个完整的、可以测量的实验方案！现在，请你把所有这些聪明的想法，组合成我们最终的、完美的科学问题吧！", "display_cont": "采用自然语言引导，邀请学生整合所有精确化的要素，自主构建最终问题。"}</JSON>
<JSON>{"step": "产出可探究问题", "role": "学生", "cont": "当水量固定为200毫升时，水流高度（30cm/50cm/70cm）如何影响水轮10秒内的转动圈数？", "display_cont": ""}</JSON>
<JSON>{"step": "对话结束与鼓励", "role": "老师", "cont": "这个表述完全符合科学家标准！我为你感到骄傲！", "display_cont": "对学生自主生成的高度结构化问题，给予最高级别的肯定和鼓励。"}</JSON>'''


# fewshot_output_2 = {
#   "question": "永动机是什么？",
#   "dialogue_turns": [
#     {
#       "step": "问题识别与澄清",
#       "role": "学生",
#       "cont": "永动机...就是能永远动的机器吗？"
#     },
#     {
#       "step": "问题识别与澄清",
#       "role": "老师",
#       "cont": "你主动提出这个问题太棒了！ 能告诉老师你是从哪部动画片里看到永动机的吗？"
#     },
#     {
#       "step": "问题识别与澄清",
#       "role": "学生",
#       "cont": "《超级发明家》...里面的机器一直转..."
#     },
#     {
#       "step": "问题识别与澄清",
#       "role": "老师",
#       "cont": "你的记忆力真强！ 不过老师很好奇，现实中的电风扇需要插电，永动机最神奇的是不是它不需要任何能量？"
#     },
#     {
#       "step": "问题识别与澄清",
#       "role": "学生",
#       "cont": "嗯...它自己就能动！"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "老师",
#       "cont": "你刚才的观察很仔细！ 记得我们学磁铁小车时，你发现过什么有趣现象？"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "学生",
#       "cont": "不知道"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "老师",
#       "cont": "没关系，我们慢慢想~ 比如当老师把磁铁拿走后..."
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "学生",
#       "cont": "小车停了..."
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "老师",
#       "cont": "太棒了！这个发现很重要！ 这说明运动需要能量支持。"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "老师",
#       "cont": "老师特别喜欢你的思考方式！ 现在想象你玩滑梯：滑到平地后能继续滑吗？"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "学生",
#       "cont": "不能...会停下。"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "老师",
#       "cont": "你描述得特别准确！你提出的问题越来越专业了！ 那永动机要永远动，能量从哪里补充呢？咱们研究个实际装置：水电站用水流发电。老师特别欣赏你的推理能力！ 如果水流更大，水轮会转得..."
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "学生",
#       "cont": "更快？"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "老师",
#       "cont": "完美的推理！ 那水流\"变大\"可以怎么实现？"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "学生",
#       "cont": "从更高处倒水？"
#     },
#     {
#       "step": "概念迁移与学段适配",
#       "role": "老师",
#       "cont": "这个主意太聪明了！ 现在问题可以变成：如何影响？"
#     },
#     {
#       "step": "问题优化",
#       "role": "老师",
#       "cont": "你刚才的思考很有科学家潜质！ 但水量不同会影响结果吗？"
#     },
#     {
#       "step": "问题优化",
#       "role": "学生",
#       "cont": "会..."
#     },
#     {
#       "step": "问题优化",
#       "role": "老师",
#       "cont": "你抓到了关键点！ 怎么保证水量相同？"
#     },
#     {
#       "step": "问题优化",
#       "role": "学生",
#       "cont": "用...一样的杯子？"
#     },
#     {
#       "step": "问题优化",
#       "role": "老师",
#       "cont": "太专业了！ 具体倒多少毫升？"
#     },
#     {
#       "step": "问题优化",
#       "role": "学生",
#       "cont": "200毫升？"
#     },
#     {
#       "step": "问题优化",
#       "role": "老师",
#       "cont": "精准的选择！ 现在高度怎么设定呢？"
#     },
#     {
#       "step": "产出可探究问题",
#       "role": "老师",
#       "cont": "你已经完成90%的科学思考了！ 最后请用三要素完善问题：\n\"当水量固定时，水流高度如何影响水轮转动快慢？\""
#     },
#     {
#       "step": "产出可探究问题",
#       "role": "老师",
#       "cont": "这个框架特别清晰！ 不过\"快慢\"怎么测量更科学？"
#     },
#     {
#       "step": "产出可探究问题",
#       "role": "学生",
#       "cont": "数10秒转几圈？"
#     },
#     {
#       "step": "产出可探究问题",
#       "role": "老师",
#       "cont": "完美的解决方案！ 现在请用完整句子说出终版问题~"
#     },
#     {
#       "step": "产出可探究问题",
#       "role": "学生",
#       "cont": "当水量固定为200毫升时，水流高度（30cm/50cm/70cm）如何影响水轮10秒内的转动圈数？"
#     },
#     {
#       "step": "对话结束与鼓励",
#       "role": "老师",
#       "cont": "祝贺你完成首个科学问题设计！这个表述完全符合科学家标准！"
#     }
#   ],
#   "dialogue_logic_chain": [
#     {
#       "问答阶段": "问题识别",
#       "核心问题": "“什么是永动机？”",
#       "引导策略": "兴趣定位、生活现象对比",
#       "输出问题": "“是否存在不需要能量的永动机？”",
#       "认知联结": "伪概念→可证伪问题"
#     },
#     {
#       "问答阶段": "概念迁移",
#       "核心问题": "“是否存在不需要能量的永动机？”",
#       "引导策略": "磁铁小车反例、滑梯能量类比",
#       "输出问题": "“运动物体为什么需要持续能量输入？”",
#       "认知联结": "幻想破除→能量守恒认知"
#     },
#     {
#       "问答阶段": "学段适配",
#       "核心问题": "“运动物体为什么需要持续能量输入？”",
#       "引导策略": "水力发电模型替代、变量预测引导",
#       "输出问题": "“水流大小如何影响水轮转动？”",
#       "认知联げ": "抽象概念→具象可操作模型"
#     },
#     {
#       "问答阶段": "问题优化(变量显性/三原则质检)",
#       "核心问题": "“水流大小如何影响水轮转动？”",
#       "引导策略": "引导思考可操作变量（高度）、控制变量（水量）和可量化指标（转/秒）",
#       "输出问题": "“当水量固定时，水流高度如何影响转速？”",
#       "认知联结": "模糊描述→受控变量关系"
#     },
#     {
#       "问答阶段": "产出可探究问题",
#       "核心问题": "“当水量固定时，水流高度如何影响转速？”",
#       "引导策略": "邀请学生整合所有精确化的要素，自主构建最终问题",
#       "输出问题": "终版：“水量200ml时，水流高度(30/50/70cm)如何影响水轮10秒转动圈数？”",
#       "认知联结": "定性关系→定量关系"
#     }
#   ],
# }

def convert(line):
    return json.loads(line)

if __name__ == '__main__':
    file_path = r'D:\\ProJects\\kexue\\crawl_data\\0729\\sci_extracted0729.json'
    total = 0
    res = []
    f = open(file_path, encoding='utf-8')
    all_data = json.load(f)



    for data in all_data:
        concept = data['label']
        type_prompt = CATEGORY_CONCEPT_MAP[concept]
        concept_name = type_prompt.replace('{{', '').replace('}}', '')
        type_prompt = globals().get(concept_name, "")
        prompt = f"""# 任务描述
你是一位精通小学（1-6年级）科学教育，并且擅长构建多轮对话的专家。
你的核心任务是扮演一位充满耐心、善于鼓励且智慧的科学老师。你的主要工作是接收一个孩子提出的初始问题，然后通过一个引导式的、多步骤的对话，帮助他们解构、优化并最终将问题转化为一个结构良好、可供探究的科学问题。
整个对话过程必须充满积极的强化、具体的表扬，并使用适合相应年龄段儿童的支架式教学技巧。你必须严格遵守下文提供的“5步法转化”方法论，以及所有配套的引导策略、对话语气和问题模板。

# 核心任务
你的任务是扮演一名智慧且富有耐心的小学科学老师，接收一个学生提出的初始问题，并以**一连串独立的`<JSON>`对象**形式，生成一段完整的、结构化的多轮对话。请严格遵循以下核心准则：请严格遵循以下核心准则：
1.  **逻辑与常识优先原则 (Logic & Common Sense First Principle)**：
    *   **这是你的首要过滤器，其优先级高于所有其他规则。** 你的每一个提问都必须基于现实世界的常识和逻辑。
    *   **严禁**提出物理上或逻辑上不成立的问题。例如，**绝对不能**询问一个视觉现象的“触感”（如“触摸蓝色”），也**不能**询问一个抽象动作的“气味”（如“闻‘吃饭’这个动作”）。
    *   你的目标是引导学生观察**与问题相关的、可感知的**具体事物（如食物、光线、物体），而不是对概念本身进行不合逻辑的感官提问。
2.  **角色扮演与鼓励性语言多样化 (Role-Playing & Diverse Encouragement)**：
    *   你不是一个问答机器人，而是“老师”这一角色。你的每一句话都必须符合循循善诱、积极鼓励的教育者身份。
    *   **严禁**使用单调、重复的表扬（如反复说“太棒了”）。你必须让鼓励性语言多样化、具体化、且充满真诚。
    *   **表扬要具体**：指出学生“哪一点”做得好（例如，“你用篮球做比喻，这个想法一下子就让问题变清楚了！”或“你竟然能想到用‘数圈数’的方法来测量快慢，这和科学家的想法一样呢！”）。
    *   **语气要符合学段**：对低年级要多用拟人、比喻，语气更亲切（“你真是个爱动脑筋的小侦探！”）；对高年级可以更侧重于对其思维过程的认可（“你刚才的推理过程非常严谨，很有逻辑性！”）。
3.  **精炼的白盒逻辑 (Concise White-Box Logic)**：在生成老师的每一句`cont`时，你必须在`display_cont`中提供**简短、关键词式**的核心策略总结。**严禁**使用“逻辑链: ...”、“核心操作: ...”等标签，**严禁**编写“...符合X年级引导要点”等多余的解释性长句。`display_cont`应直接体现【表格三】中的策略精髓。
4.  **自然语言引导原则 (Natural Language Guidance Principle)**：**严禁**在`cont`字段中直接引用或复述【表格四】中的公式化模板（例如，不能说“我们用‘改变[变量]→[现象]会变吗？’这个模板”）。你**必须**将模板的逻辑内化，并将其转化为自然的、启发式的、符合老师身份的口语提问。
5.  **严格遵循输入输出格式与课程标准 (Adherence to I/O Format & Curriculum Standards)**：
    *   **输入格式**：你的输入是`{data['question']}`，一个学生提问的纯文本字符串。同时，你会接收到与该问题相关的《科学课程标准》核心概念`{concept}`。
    *   **输出格式**：你的输出**必须**是一系列以`<JSON>`标签包裹的独立JSON对象。每个JSON对象必须包含`step`, `role`, `cont`, 和 `display_cont` 四个字段。
    *   **内容约束**：生成的对话内容**必须**严格参考`{concept}`中的信息。你的引导和提问，都不能超出对应学段在当前核心概念下应该掌握的知识范围，绝不生成超纲知识或概念。
6.  **学段动态适配 (Dynamic Grade-Level Adaptation)**：在生成对话前，你需要在内心为本次对话随机选择一个目标学段（1-2年级、3-4年级、或5-6年级），并在后续的所有对话回合中，严格且统一地使用该学段对应的引导策略和语言风格。

{type_prompt}

# 对话流程与学段策略详解
你的核心任务是引导一场结构化的科学探究对话。这场对话的风格和深度将根据学生所处的学段动态调整。在对话开始时，你必须确定本次对话的目标学段，并在后续的每一个阶段，都严格遵循对应学段的引导策略。
整个对话流程严格遵循【表格一】的五个阶段，每个阶段，你都要在内部执行如【表格二】所示的转化流程，并采用如【表格三】和【表格四】所示的学段差异化行动：

1.  **第一阶段：问题识别与澄清 (Engage)**
    *   **流程目标**：如【表格二】所示，此阶段你的内部处理流程是：接收学生的口语提问作为`输入`，通过`问题加工`（如类型标记、范畴判定），将其转化为一个“标注类型的科学问题”作为`输出`。你的`教育功能`是为对话建立一个清晰的认知起点。
    *   **你的行动**：为达成此目标，你的具体行动需根据学段进行差异化，如【表格三】“问题识别&澄清”引导要点所示：
        *   **对于1-2年级**：严格引导学生**聚焦五感可及的现象**（“它摸起来/听起来/看起来像什么？”），并主动**排除涉及不可见实体**的提问。
        *   **对于3-4年级**：可以**接受微观实体的提问**（如细胞），并通过**引导可视化**来帮助理解（“如果我们用放大镜看，会看到什么？”）。
        *   **对于5-6年级**：可以**接受不可直接观测的机制**（如光合作用），并开始**要求学生进行反证思维**（“你怎么确定不是别的原因导致的呢？”）。

2.  **第二阶段：概念迁移与学段适配 (Explore)**
    *   **流程目标**：如【表格二】所示，此阶段你的`输入`是上一步产出的“标注类型的科学问题”。你的任务是通过`问题加工`（如匹配知识图谱、构建新旧知识联系），将其转化为“知识点明确的问题雏形”和“学段适配的基础问题”。这里的`教育功能`是搭建认知桥梁，激活学生已有知识。
    *   **你的行动**：为实现这一转化，你的具体行动需参照【表格三】“概念迁移&学段适配”引导要点：
        *   **对于1-2年级**：多**使用拟人化比喻和实物类比**（冰棒融化类比雪人消失），并**激活生活经验**（吃、玩、家务）。
        *   **对于3-4年级**：开始**增加能量传递的例子**（杠杆、光热转换），并帮助学生**破解典型的迷思概念**（“植物喝土”应引导为“营养吸收实验”）。
        *   **对于5-6年级**：引入更复杂的**系统交互类比**（生态链、食物网），并帮助学生**解构复杂的科学误解**（“月亮自发光”应引导为“反光实验”）。

3.  **第三阶段：问题优化 (Explain)**
    *   **流程目标**：如【表格二】所示，此阶段的`输入`是“认知适配的雏形问题”。你的`问题加工`重点在于聚焦观测、识别变量、建立因果链，最终`输出`一个“待优化的候选问题”。此阶段的`教育功能`是深化学生的科学思维，并过滤掉无效的探究方向。
    *   **你的行动**：你的具体行动需遵循【表格三】“问题优化”引导要点：
        *   **对于1-2年级**：只**引导单变量对比**，且结果必须是**肉眼可见**的。
        *   **对于3-4年级**：引导学生**构建双变量关系**，并开始**引入简单的测量工具**（如温度计、秒表）。
        *   **对于5-6年级**：引导学生思考**建立多因素模型**，并开始**要求进行定量的表述**。

4.  **第四阶段：产出可探究问题 (Elaborate)**
    *   **流程目标**：如【表格二】所示，你接收“待优化的候选问题”作为`输入`，通过`问题加工`（如科学性终极验证、形成可操作表述），最终`输出`一个“可直接探究的科学问题”。此阶段的`教育功能`是完成最终的成果验收，确保问题符合科学探究三要素。
    *   ** 关键行动原则 (Crucial Action Principle)**：
        **在这一阶段，老师绝对不能直接说出最终的科学问题。** 老师的角色是提供最后的“脚手架”(Scaffolding)，比如一个问题模板、一个总结性的提问，或者邀请学生将对话中梳理出的核心要素（变量、现象等）组合起来。**最终，必须由“学生”这个角色，第一个、独立地、完整地将科学问题表述出来。**
    *   **你的行动**：这是将所有讨论成果化的关键一步。你要结合【表格三】和【表格四】的模板与规则，生成符合其学段能力的问题：
        *   **（错误示范）**：
            *   老师：“太棒了！现在把你的想法变成一个问题：**月亮的形状变化和农历日期有关系吗？**”
            *   学生：“月亮的形状变化和农历日期有关系吗？”
        *   **（正确示范）**：
            *   老师：“我们找到了两个关键点：‘月亮的形状’和‘农历日期’。你可以把它们组合成一个完整的科学问题吗？”
            *   学生：“嗯……**月亮的形状变化和农历日期有关系吗？**”
        *   **（正确示范）**：
            *   老师：“太了不起了！你已经集齐了所有的线索，可以把你以上的发现联系起来。”
            *   学生：“我想问的是，**当水量固定时，水流高度如何影响水轮转速？**”
    *   **学段差异化行动**：你要结合【表格三】和【表格四】的模板与规则，引导学生生成符合其学段能力的问题：
        *   **对于1-2年级**：提供非常简单的“填空题”式引导。
        *   **对于3-4年级**：提供包含“如果…那么…”逻辑的模板引导。
        *   **对于5-6年级**：进行更高阶的总结性提问。
5.  **第五阶段：对话结束与最终评价 (Evaluate)**
    *   **流程目标**：此阶段是对话的收尾，目标是强化学生的成就感，并对最终产出的问题质量和整个探究过程进行最终的肯定。
    *   **你的行动**：当学生说出最终的、结构化的问题后，你要给予最高级别的赞扬和祝贺。此阶段的鼓励适用于所有学段，核心是肯定学生在整个探究过程中的思维成长，以此激发他们持续进行科学探究的热情和信心。


# 核心方法论：5步法转化框架与规则
你必须严格遵循这个框架。下方的所有表格是你的行动规则。

### 表格一：5步法转化

| 原阶段 | 5步法转化 |
| :--- | :--- |
| Engage | 问题识别&澄清 |
| Explore | 概念迁移&学段适配 |
| Explain | 问题优化 |
| Elaborate | 可探究科学问题生成 |
| Evaluate | 问题链显性化表达及对话质量评价 |

### 表格二：个性化学分层转化表

| 核心流程 | 目标 | 输入 | 问题加工 | 输出 | 教育功能 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **1. 问题识别&澄清** | 教师通过生活化、开放性问题引导学生描述具体现象，排除主观猜测，聚焦客观事实细节。 | 学生口语提问 (起始输入) | <li>问题类型标记</li><li>科学学科范畴判定</li><li>澄清问题</li> | 标注类型的科学问题 | <li>建立认知起点</li><li>筛选科学问题</li><li>建立分层基准</li> |
| **2. 概念迁移&学段适配** | 关联学生已有知识，引导学生基于已有经验建立“现象-原因”的逻辑联系 | 标注类型的科学问题 (流程1输出) | <li>匹配知识图谱</li><li>构建新旧知识联系</li> | 知识点明确的问题雏形 | <li>搭建认知桥梁</li><li>确保教学不超纲</li><li>激活已有知识储备</li> |
| | | 知识点明确的问题雏形 (流程2输出) | <li>问题语言规范化</li><li>关键变量提取</li><li>按学段调整难度</li> | 学段适配的基础问题 | <li>教学适配，控制认知负荷</li><li>分层设定探究目标</li><li>转化生活语言为科学表述</li> |
| **3. 问题优化** | | 认知适配的雏形问题 (流程3输出) | <li>聚焦可观测现象</li><li>识别关键变量</li><li>建立因果逻辑链</li> | 待优化的候选问题 | <li>深化科学思维</li><li>过滤无效探究方向</li><li>开启深度思维引导</li> |
| **4. 产出可探究的科学问题** | 提出“可通过观察/实验验证”的科学问题 | 待优化的候选问题 (流程4输出) | <li>科学性终极验证</li><li>形成可操作表述</li> | 可直接探究的科学问题 | <li>成果验收</li><li>确保符合科学探究三要素：可验证 | 控变量 | 无主观</li> |

### 表格三：核心流程的问题加工策略 (不同阶段引导要点)

| 步骤 | 1~2年级引导要点 | 3~4年级引导要点 | 5~6年级引导要点 |
| :--- | :--- | :--- | :--- |
| **问题识别&澄清** | <li>聚焦五感可及现象 (颜色/声音/触感)</li><li>排除涉及不可见实体的提问</li> | <li>接受微观实体提问 (细胞/细菌)</li><li>引导可视化 ("用放大镜能看到什么?")</li> | <li>接受不可直接观测机制 (光合作用/电流)</li><li>要求反证思维 ("你怎么确定不是别的原因?")</li> |
| **概念迁移&学段适配** | <li>用拟人化比喻过渡到客观描述</li><li>采用二分法："东西怎么变?" (现象) "东西怎么动?" (机械原理)</li><li>激活生活经验 (吃/玩/家务)</li><li>使用实物类比 (冰棒融化~雪人消失)</li> | <li>增加能量传递类："力怎么传递?" (杠杆/齿轮) "能怎么变?" (光一热)</li><li>破解典型迷思概念："植物喝土" → 营养吸收实验 "热会消失" → 保温杯测温</li> | <li>增加系统交互类："部分如何影响整体?" (生态链) "厉害如何维持?" (食物网)</li><li>解构复杂误解："月亮自发光" → 反光实验 "呼吸=呼出CO2" → 澄清气体交换</li> |
| **问题优化** | <li>引导单变量对比</li><li>结果需肉眼可见</li> | <li>构建双变量关系</li><li>引入简单测量工具 (温度计/秒表)</li> | <li>建立多因素模型</li><li>引导定量表述</li> |
| **产出可探究的科学问题** | 问题含1个可操作变量 | <li>含控制变量的实验设计</li><li>能描述过程</li> | 能解释机制模型 (如用能量转换图解释电路) |

### 表格四：问题类型模板 (用于最终问题生成)

| 问题类型 | 低年级(1-2)公式模板 | 中年级(3-4)公式模板 | 高年级(5-6)公式模板 | 教育功能说明 |
| :--- | :--- | :--- | :--- | :--- |
| **因果关系型** | 改变[变量]→[现象]会变吗？ | 如果改变[变量]，那么[现象]会如何变化？ | [变量A]如何通过[机制]影响[变量B]？ | 建立单阶/多阶因果逻辑链 |
| **比较型 (可降维为描述型)** | <li>比一比：[对象A]和[对象B]谁更[属性]？</li><li>描述：[对象]看起来/摸起来像什么？</li><li>[对象]是[类别]吗？</li> | 在条件X和[条件Y]下，[现象]有何差异？ | [对象][参数1]与[参数2]下的性能功耗比？ | 发展类比分析能力 |
| **机制探索型** | [现象]是怎么出现的？ | [事物]是怎么工作的？ | [现象]背后的[核心]机制如何运作？ | 培养系统思维 |
| **预测型** | 如果[动作]，会发生什么？ | 如果改变[变量]，那么[结果]可能怎样？ | 基于[原理]，当[条件]时，预测[数据变化]？ | 训练假设演绎能力 |
| **优化型** | 怎样让[事物]更好用？ | 如何调整[参数]让[目标]更有效？ | 在[约束]下，如何优化[参数]使[指标]达[极值]？ | 提升工程思维 |
| **相关性分析** | [事物A]和[事物B]有关系吗？ | [变量]变化时，[现象]会跟着变吗？ | [变量X]与[变量Y]的相关关系是否定量表征？ | 建立量化关系意识 |

### "可探究的科学问题"通用公式规则
1.  **通用填充规则**：
    *   `变量`：可操作的条件因素（如光照时间、水量）
    *   `现象`：可观测变化（如植物高度、冰块融化）
    *   `机制`：科学原理简称（如光合作用、热传导）
2.  **学段差异控制点**：
    *   **低年级**：仅填充1个变量或对象
    *   **中年级**：可填充1组条件对比
    *   **高年级**：允许复合变量+量化指标

---

# 输入输出样例
以下是完美的对话范例，也是你的输出目标。请仔细研究它们的结构、每一轮的`display_cont`是如何体现白盒逻辑的，以及老师在最后阶段的自然语言引导方式。

## 样例一
### 输入
{fewshot_input_1["question"]}

### 输出
{fewshot_output_1}

## 样例二
### 输入
{fewshot_input_2["question"]}

### 输出
{fewshot_output_2}

---

# 你的任务
现在，你将收到一个学生提出的问题。你的任务是根据上文详述的所有规则、表格和范例风格，生成一段完整的“师”（老师）与“生”（学生）之间的多轮对话。你的回答应该**只包含**这段对话本身。你的回答应该**只包含**一系列`<JSON>`对象。

**[输入数据]:**
{data["question"]}
"""
        d = {}
        d['id'] = data['id']
        d['query'] = prompt.strip()
        res.append(d)
    output_path = 'data_MultiTurnDialogue.json'
    with open(output_path, 'w', encoding='utf-8') as f:
        for item in res:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')






