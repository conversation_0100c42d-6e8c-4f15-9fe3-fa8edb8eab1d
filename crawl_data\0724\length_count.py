"""
统计merged_sci_data.json中answer字段各子字段的长度
"""

import json
import os


def count_answer_lengths(data):
    """统计每条记录的answer子字段长度"""
    length_stats = []
    
    for item in data:
        item_id = item.get('id', '')
        answer = item.get('answer', {})
        
        # 统计各子字段长度
        answer_length = {
            "居里夫人_小行动": len(answer.get("居里夫人_小行动", "")),
            "居里夫人_冷知识": len(answer.get("居里夫人_冷知识", "")),
            "达尔文_小行动": len(answer.get("达尔文_小行动", "")),
            "达尔文_冷知识": len(answer.get("达尔文_冷知识", ""))
        }
        
        length_stats.append({
            "id": item_id,
            "answer_length": answer_length
        })
    
    return length_stats


def main():
    """主函数"""
    # 定义文件路径
    base_dir = r"D:\ProJects\kexue\crawl_data\0724"
    input_file = os.path.join(base_dir, "length_no_limit", "merged_sci_data.json")
    output_file = os.path.join(base_dir, "answer_length_stats.json")
    
    # 加载数据
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 统计长度
    length_stats = count_answer_lengths(data)
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(length_stats, f, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    main()
