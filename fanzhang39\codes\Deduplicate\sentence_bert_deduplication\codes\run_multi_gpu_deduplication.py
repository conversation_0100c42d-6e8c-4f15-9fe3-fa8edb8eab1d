"""
多GPU并行BGE-M3文本去重系统
使用4张Tesla T4并行处理文件
"""

# 解决TLS冲突问题 - 必须在导入其他库之前设置
import os
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'

import sys
from pathlib import Path
import logging
import torch

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from bge_m3_deduplication import MultiGPU_BGE_M3_Processor
from config import Config

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, Config.LOG_LEVEL),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('multi_gpu_deduplication.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def check_gpu_availability():
    """检查GPU可用性"""
    if not torch.cuda.is_available():
        raise RuntimeError("CUDA不可用，无法使用GPU")
    
    num_gpus = torch.cuda.device_count()
    if num_gpus == 0:
        raise RuntimeError("没有检测到GPU设备")
    
    logger = logging.getLogger(__name__)
    logger.info(f"检测到 {num_gpus} 个GPU设备:")
    
    for i in range(num_gpus):
        props = torch.cuda.get_device_properties(i)
        total_memory = props.total_memory / 1024**3
        allocated_memory = torch.cuda.memory_allocated(i) / 1024**3
        usage_percent = (allocated_memory / total_memory) * 100
        logger.info(f"  GPU {i}: {props.name}, {total_memory:.1f}GB, {usage_percent:.1f}% 已使用")
    
    return num_gpus

def main():
    """主函数 - 多GPU并行处理"""

    # 设置多进程启动方式为spawn（解决CUDA fork问题）
    import multiprocessing as mp
    mp.set_start_method('spawn', force=True)

    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("=== 多GPU并行BGE-M3文本去重系统启动 ===")

    try:
        # 验证配置
        Config.validate_paths()
        logger.info("配置验证通过")
        
        # 检查GPU可用性
        num_gpus = check_gpu_availability()
        
        # 显示GPU状态信息
        gpu_info = Config.get_gpu_memory_info()
        if gpu_info:
            logger.info("=== GPU状态信息 ===")
            for device, info in gpu_info.items():
                logger.info(f"{device}: {info['usage_percent']:.1f}% 已使用, {info['free']:.1f}GB 可用")
        
        # 使用配置中的GPU优先级顺序
        preferred_gpu_order = Config.PREFERRED_GPU_ORDER
        min_memory_gb = Config.MIN_GPU_MEMORY_GB
        available_gpus = []

        for gpu_id in preferred_gpu_order:
            if gpu_id < num_gpus:
                allocated = torch.cuda.memory_allocated(gpu_id) / 1024**3
                total = torch.cuda.get_device_properties(gpu_id).total_memory / 1024**3
                free_memory = total - allocated

                # 使用配置中的最小内存要求
                if free_memory >= min_memory_gb:
                    available_gpus.append((gpu_id, free_memory))
                    logger.info(f"GPU {gpu_id} 可用: {free_memory:.1f}GB 空闲内存")
                else:
                    logger.warning(f"GPU {gpu_id} 内存不足: 只有 {free_memory:.1f}GB 空闲内存")

        if len(available_gpus) == 0:
            raise RuntimeError("没有足够空闲内存的GPU可用")

        # 使用4张GPU进行并行处理
        num_gpus_to_use = min(4, len(available_gpus))
        logger.info(f"将使用 {num_gpus_to_use} 张GPU进行并行处理")

        # 提取选中的GPU ID列表
        selected_gpu_ids = []
        for i in range(num_gpus_to_use):
            gpu_id, free_mem = available_gpus[i]
            selected_gpu_ids.append(gpu_id)
            logger.info(f"GPU {gpu_id}: {free_mem:.1f}GB 空闲内存")

        # 创建多GPU处理器 - 传递正确的GPU ID列表
        processor = MultiGPU_BGE_M3_Processor(
            model_path=Config.MODEL_PATH,
            num_gpus=num_gpus_to_use,  # 使用选中的GPU数量
            gpu_ids=selected_gpu_ids  # 传递具体的GPU ID列表
        )
        
        # 开始并行处理
        processor.process_all_files_parallel(
            input_path=Config.INPUT_PATH,
            output_dir=Config.OUTPUT_PATH,
            thresholds=Config.SIMILARITY_THRESHOLDS
        )
        
        # 输出结果摘要
        logger.info("=== 处理结果摘要 ===")
        logger.info(f"结果保存在: {Config.OUTPUT_PATH}")
        
        for threshold in Config.SIMILARITY_THRESHOLDS:
            threshold_dir = os.path.join(Config.OUTPUT_PATH, f"output_{threshold}")
            if os.path.exists(threshold_dir):
                files_count = len([f for f in os.listdir(threshold_dir) if f.endswith('.json')])
                logger.info(f"  阈值 {threshold}: {files_count} 个去重文件")
        
        logger.info("=== 多GPU并行处理全部完成 ===")
        
    except Exception as e:
        logger.error(f"系统运行出错: {e}")
        raise

if __name__ == "__main__":
    main()
