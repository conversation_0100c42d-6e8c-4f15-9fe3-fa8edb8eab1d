"""
完整流程测试脚本
从每个输入文件中提取100个数据进行小规模测试
"""

# 解决TLS冲突问题
import os
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'

import sys
import json
import logging
from pathlib import Path
import shutil

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from config import Config

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_full_pipeline.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def create_test_data():
    """从每个输入文件中提取100个数据创建测试数据"""
    logger = logging.getLogger(__name__)
    
    input_path = Config.INPUT_PATH
    test_data_dir = "test_data_pipeline"
    
    # 创建测试数据目录
    os.makedirs(test_data_dir, exist_ok=True)
    
    logger.info(f"从 {input_path} 提取测试数据到 {test_data_dir}")
    
    # 获取所有JSON文件
    if not os.path.exists(input_path):
        logger.error(f"输入路径不存在: {input_path}")
        return False
    
    json_files = [f for f in os.listdir(input_path) if f.endswith('.json')]
    if not json_files:
        logger.error(f"在 {input_path} 中没有找到JSON文件")
        return False
    
    logger.info(f"找到 {len(json_files)} 个JSON文件")
    
    extracted_files = []
    
    for json_file in json_files:
        source_file = os.path.join(input_path, json_file)
        test_file = os.path.join(test_data_dir, json_file)
        
        try:
            # 读取原始文件
            with open(source_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                logger.warning(f"跳过 {json_file}: 不是列表格式")
                continue
            
            # 提取前100个数据
            sample_size = min(100, len(data))
            sample_data = data[:sample_size]
            
            # 保存测试数据
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"提取 {json_file}: {len(data)} -> {sample_size} 个问题")
            extracted_files.append(json_file)
            
        except Exception as e:
            logger.error(f"处理 {json_file} 时出错: {e}")
            continue
    
    logger.info(f"成功提取 {len(extracted_files)} 个测试文件")
    return len(extracted_files) > 0

def run_test_pipeline():
    """运行测试流程"""
    logger = logging.getLogger(__name__)
    
    # 临时修改配置为测试数据
    original_input_path = Config.INPUT_PATH
    original_output_path = Config.OUTPUT_PATH
    
    test_input_path = "test_data_pipeline"
    test_output_path = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/outputs/test_pipeline"
    
    try:
        # 修改配置
        Config.INPUT_PATH = test_input_path
        Config.OUTPUT_PATH = test_output_path
        
        logger.info("=== 开始测试完整流程 ===")
        logger.info(f"测试输入路径: {test_input_path}")
        logger.info(f"测试输出路径: {test_output_path}")
        
        # 导入并运行多GPU处理器
        from run_multi_gpu_deduplication import main as run_main
        
        # 运行主流程
        run_main()
        
        logger.info("=== 测试流程完成 ===")
        
        # 检查输出结果
        if os.path.exists(test_output_path):
            output_files = []
            for root, dirs, files in os.walk(test_output_path):
                for file in files:
                    if file.endswith('.json'):
                        output_files.append(os.path.join(root, file))
            
            logger.info(f"生成了 {len(output_files)} 个输出文件:")
            for output_file in output_files:
                rel_path = os.path.relpath(output_file, test_output_path)
                logger.info(f"  {rel_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试流程失败: {e}")
        return False
        
    finally:
        # 恢复原始配置
        Config.INPUT_PATH = original_input_path
        Config.OUTPUT_PATH = original_output_path

def validate_test_results():
    """验证测试结果"""
    logger = logging.getLogger(__name__)
    
    test_output_path = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/outputs/test_pipeline"
    
    if not os.path.exists(test_output_path):
        logger.error("测试输出目录不存在")
        return False
    
    logger.info("=== 验证测试结果 ===")
    
    # 检查阈值目录
    threshold_dir = os.path.join(test_output_path, "output_0.95")
    if not os.path.exists(threshold_dir):
        logger.error("阈值0.95的输出目录不存在")
        return False
    
    # 统计输出文件
    output_files = [f for f in os.listdir(threshold_dir) if f.endswith('.json') and not f.endswith('_mapping.json')]
    mapping_files = [f for f in os.listdir(threshold_dir) if f.endswith('_mapping.json')]
    report_files = [f for f in os.listdir(threshold_dir) if f.endswith('_report.txt')]
    
    logger.info(f"去重文件: {len(output_files)} 个")
    logger.info(f"映射文件: {len(mapping_files)} 个")
    logger.info(f"报告文件: {len(report_files)} 个")
    
    # 验证每个输出文件
    for output_file in output_files:
        file_path = os.path.join(threshold_dir, output_file)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                logger.info(f"  {output_file}: {len(data)} 个去重后的问题")
            else:
                logger.warning(f"  {output_file}: 格式异常")
                
        except Exception as e:
            logger.error(f"  {output_file}: 读取失败 - {e}")
    
    logger.info("=== 结果验证完成 ===")
    return True

def cleanup_test_data():
    """清理测试数据"""
    logger = logging.getLogger(__name__)
    
    test_data_dir = "test_data_pipeline"
    test_output_path = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/outputs/test_pipeline"
    
    try:
        # 清理测试输入数据
        if os.path.exists(test_data_dir):
            shutil.rmtree(test_data_dir)
            logger.info(f"已清理测试输入数据: {test_data_dir}")
        
        # 询问是否清理测试输出数据
        logger.info(f"测试输出数据保留在: {test_output_path}")
        logger.info("如需清理，请手动删除该目录")
        
    except Exception as e:
        logger.error(f"清理测试数据时出错: {e}")

def main():
    """主测试函数"""
    setup_test_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=== BGE-M3 完整流程测试开始 ===")
    
    try:
        # 步骤1: 创建测试数据
        logger.info("步骤1: 创建测试数据")
        if not create_test_data():
            logger.error("创建测试数据失败")
            return False
        
        # 步骤2: 运行测试流程
        logger.info("步骤2: 运行测试流程")
        if not run_test_pipeline():
            logger.error("测试流程失败")
            return False
        
        # 步骤3: 验证测试结果
        logger.info("步骤3: 验证测试结果")
        if not validate_test_results():
            logger.error("结果验证失败")
            return False
        
        # 步骤4: 清理测试数据
        logger.info("步骤4: 清理测试数据")
        cleanup_test_data()
        
        logger.info("=== 完整流程测试成功完成 ===")
        logger.info("✅ 所有组件工作正常")
        logger.info("✅ GPU分配逻辑正确")
        logger.info("✅ 文件处理流程正常")
        logger.info("✅ 输出格式正确")
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
