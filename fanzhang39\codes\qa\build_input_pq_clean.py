import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys


def build_prompt(input):
    return f'''
# 任务描述
假设你是一位科学教师，请参考【科学问题定义】，仔细理解问题列表中的每一个问题，判断是否是科学问题。
如果是的话，请从【科学问题分类】中选出最恰当的类别；如果不是的话，类别输出为“非科学”即可。
请参考样例，以json形式输出，并且严格按照样例输出格式，保证输出与输入数量一致，且严格一一对应，不要输出其他内容！

【科学问题定义】
如果问题与自然现象、事件或过程有关，并且可以通过科学方法（如观察、实验或理论分析）进行研究，那么它是一个科学问题。比如“为什么苹果会掉地上？”、“把树叶都拔光，树会死吗？”、“为什么太阳会发光？”。

【科学问题分类】
-物理：物理学研究自然界的基本规律和现象，特别是关于物质、能量、力和运动的基本性质和相互作用。物理学的问题通常涉及到理解物体如何运动、力如何作用在物体上、以及能量如何转换和守恒。它包括经典力学、电磁学、热力学、量子力学、相对论等子领域。例如“物体如何运动？”、“光是如何传播的？”、”重力是如何影响物体的？“。

-化学：化学是研究物质的组成、性质、结构以及它们之间的化学反应的科学。化学问题通常涉及到原子和分子如何相互作用以形成新的物质、化学反应的能量变化、以及不同物质的化学性质。化学包括有机化学、无机化学、物理化学、分析化学等分支。例如“为什么铁会生锈？”、“化学反应中的能量是如何变化的？”、“水的化学结构是什么？”。

-生物：生物学研究生命的本质、特性和过程，包括从微观的细胞生物学到宏观的生态学和进化生物学。生物学问题通常涉及到理解生物体的结构和功能、遗传信息如何传递、以及生物体如何与其环境相互作用。生物学包括细胞生物学、遗传学、生态学、进化生物学等领域。例如“细胞是如何工作的？”、“基因是如何遗传的？”、“动植物如何适应它们的环境？”。

-地理：地理学研究地球的表面特征、自然现象、以及人类活动如何与这些特征和现象相互作用。地理学问题可以涉及地球的物理结构、气候变化、自然资源的分布、以及人类社会如何利用和影响环境。地理学包括自然地理学、人文地理学、环境地理学等分支。例如“火山是如何形成的？”、“气候变化如何影响全球生态系统？”、“城市化对环境的影响是什么？”。

-其他：除了上述四大类，科学还包括许多其他学科，如天文学、地质学、环境科学、心理学、计算机科学、技术与工程等，这些均统一为其他学科。这些学科通常会跨越传统的科学领域，涉及更为复杂和综合的科学问题。例如“行星是如何形成的？”、“心理状态如何影响人类行为？”、“计算机是如何处理和存储信息的？”。

# 样例
## 问题列表
{fewshot_input}
## 输出
{fewshot_output}

# 当前输入
## 问题列表
{input}

# 任务重述
请参考样例，并以json形式输出问题列表中的每一个问题的类别。
'''


fewshot_input = [
    {"id": "0", "content": "今天天津市河北区的天气是什么样子的？"},
    {"id": "1", "content": "火山爆发是因为什么？"},
    {"id": "2", "content": "我想了解一下海王星。"}
]

fewshot_output = [
    {"id": "0", "label": "非科学"},
    {"id": "1", "label": "地理"},
    {"id": "2", "label": "其他"}
]


def convert(line):    
    return json.loads(line)


data_path = '/data/chexam/fanzhang39/projects/lm-0809/kexue/qa/query_clean.json'
save_file = '/work1/data/fanzhang39/0624/kexue_20250624_cls2.json'

data_path = '/data/chexam/fanzhang39/projects/lm-0809/kexue/qa/data/raw_yf/questions_count.json'
save_file = '/work1/data/fanzhang39/0709/kexue_20250709_cls.json'
print(data_path)

total = 0
res = []
tmp = []
i = 0
f = open(data_path, encoding='utf-8')
with Pool(64) as p:
    all_data = p.imap(convert, f)
    # print(len(all_data))
    for data in all_data:
        total += 1
        if data:
            tmp.append({"id": total, "content": data['question']})
            if len(tmp) == 20:
                d = {}
                d['id'] = f'{i}-{len(tmp)}'
                d['query'] = build_prompt(tmp).strip()
                res.append(d)

                tmp = []
                i += 1

    if len(tmp):
        d = {}
        d['id'] = f'{i}-{len(tmp)}'
        d['query'] = build_prompt(tmp).strip()
        res.append(d)

# print(ans) 
# count = Counter(ans)
# print(len(res))
# print(total)
# print(count)

# save_file = os.path.join(save_dir, '语文_小学_讯飞_doubao_split_0219_50000.json')
with open(save_file, 'w', encoding='utf-8') as f:
    for item in res:
        f.write(json.dumps(item, ensure_ascii=False) + '\n')