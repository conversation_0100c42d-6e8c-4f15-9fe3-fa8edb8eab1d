import json
import os


def merge_json_files(file_path_input, file_path_output, output_path):
    with open(file_path_input, 'r', encoding='utf-8') as f:
        base_datas = json.load(f)

    answer_datas = []
    with open(file_path_output, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                answer_datas.append(json.loads(line))

    answer_map = {}
    for answer_data in answer_datas:
        answer_data_id = answer_data.get('id')
        answer_data_string = answer_data.get('answer')
        if answer_data_id and answer_data_string:
            parsed_answer = json.loads(answer_data_string)
            final_answer = parsed_answer.get('answer')
            if final_answer is None:
                final_answer = parsed_answer
            answer_map[answer_data_id] = final_answer



    merged_data = []
    for base_item in base_datas:
        item_id = base_item.get('id')
        if item_id in answer_map:
            new_item = base_item.copy()
            if "output" in new_item:
                new_item["input"] = new_item.pop("output")
            new_item["answer"] = answer_map[item_id]
            merged_data.append(new_item)

    output_dir = os.path.dirname(output_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(merged_data, f, ensure_ascii=False, indent=4)

if __name__ == '__main__':
    file_path_input = "D:\\ProJects\\kexue\\crawl_data\\0723\\sci_checkout0723.json"
    file_path_output = '/crawl_data/0724/length_no_limit/资源教育-语言学习_668_doubao-1.5-pro-32k-250115_周依凡_1753327853228_sci_test_answer0724.json'
    output_path = 'D:\\ProJects\\kexue\\crawl_data\\0724\\merged_output\\merged_data0724.json'
    merge_json_files(file_path_input, file_path_output, output_path)


# # 导入需要用到的标准库：json 用于处理JSON数据，os 用于处理文件和目录路径
# import json
# import os
#
# # 定义一个函数，用于合并两个JSON文件，接收三个参数：输入文件1的路径，输入文件2的路径，以及输出文件的路径
# def merge_json_files(file_path_input, file_path_output, output_path):
#     # 使用 'with' 语句安全地打开第一个输入文件（基础数据文件）
#     with open(file_path_input, 'r', encoding='utf-8') as f:
#         # 使用 json.load() 读取整个文件，并将其内容（一个JSON数组）解析为Python的列表
#         base_datas = json.load(f)
#
#     # 创建一个空列表，用于存放从第二个文件中逐行读取的数据
#     answer_datas = []
#     # 使用 'with' 语句安全地打开第二个输入文件（答案数据文件）
#     with open(file_path_output, 'r', encoding='utf-8') as f:
#         # 遍历文件中的每一行
#         for line in f:
#             # 检查这一行去除首尾空白后是否还有内容（即，忽略空行）
#             if line.strip():
#                 # 如果行不为空，使用 json.loads() 将这一行字符串解析成一个Python字典，并添加到列表中
#                 answer_datas.append(json.loads(line))
#
#     # 创建一个空的字典，它将作为我们的“查找表”或“映射”，用于通过ID快速找到答案
#     answer_map = {}
#     # 遍历我们刚刚从答案文件中读取并整理好的数据列表
#     for answer_data in answer_datas:
#         # 使用 .get() 方法安全地获取当前这条数据的 'id' 值，如果不存在则返回 None
#         answer_data_id = answer_data.get('id')
#         # 使用 .get() 方法安全地获取 'answer' 字段的值（它是一个包含JSON格式的字符串）
#         answer_data_string = answer_data.get('answer')
#         # 这是一个安全检查：确保 'id' 和 'answer' 字符串都成功获取到了，才继续处理
#         if answer_data_id and answer_data_string:
#             # 使用 json.loads() 解析 'answer' 字符串，将其从字符串变为一个真正的Python字典
#             parsed_answer = json.loads(answer_data_string)
#             # 尝试从解析后的字典中获取键为 'answer' 的值（处理双层嵌套的情况）
#             final_answer = parsed_answer.get('answer')
#             # 如果上一步返回了 None（说明是单层结构，没有内嵌的'answer'键）
#             if final_answer is None:
#                 # 那么，解析后的字典本身就是我们最终想要的答案
#                 final_answer = parsed_answer
#             # 在我们的查找表 answer_map 中，以数据的ID作为键，以处理好的最终答案作为值，建立映射关系
#             answer_map[answer_data_id] = final_answer
#
#
#
#     # 创建一个空列表，用于存放最终合并好的所有数据
#     merged_data = []
#     # 遍历基础数据文件中的每一条数据
#     for base_item in base_datas:
#         # 获取当前这条基础数据的 'id'
#         item_id = base_item.get('id')
#         # 检查这个 'id' 是否存在于我们创建的答案查找表 'answer_map' 中
#         if item_id in answer_map:
#             # 如果能找到匹配的答案，就复制一份当前的基础数据，以避免修改原始数据
#             new_item = base_item.copy()
#             # 检查复制后的数据中是否存在 'output' 字段
#             if "output" in new_item:
#                 # 如果存在，就使用 .pop() 方法将 'output' 的值赋给新的 'input' 字段，并同时从字典中删除 'output'
#                 new_item["input"] = new_item.pop("output")
#             # 将从答案查找表中找到的、与当前id匹配的答案，赋值给新的 'answer' 字段
#             new_item["answer"] = answer_map[item_id]
#             # 将这条完整合并、处理好的数据添加到最终结果列表中
#             merged_data.append(new_item)
#
#     # 使用 os.path.dirname() 获取输出文件路径中的目录部分
#     output_dir = os.path.dirname(output_path)
#     # 检查这个输出目录是否存在
#     if not os.path.exists(output_dir):
#         # 如果目录不存在，就使用 os.makedirs() 创建它
#         os.makedirs(output_dir)
#
#     # 使用 'with' 语句安全地打开最终的输出文件（以写入模式 'w'）
#     with open(output_path, 'w', encoding='utf-8') as f:
#         # 使用 json.dump() 将我们合并好的数据列表写入到文件中
#         # ensure_ascii=False 确保中文字符能被正确写入，而不是被转换成ASCII码
#         # indent=4 让输出的JSON文件有4个空格的缩进，格式更美观，易于阅读
#         json.dump(merged_data, f, ensure_ascii=False, indent=4)
#
# # 这是一个标准的Python入口点判断，确保只有当这个脚本被直接运行时，下面的代码才会被执行
# if __name__ == '__main__':
#     # 定义第一个输入文件（基础数据）的路径
#     file_path_input = "D:\\ProJects\\kexue\\crawl_data\\0723\\sci_checkout0723.json"
#     # 定义第二个输入文件（答案数据）的路径
#     file_path_output = 'D:\\ProJects\\kexue\\crawl_data\\0724\\资源教育-语言学习_668_doubao-1.5-pro-32k-250115_周依凡_1753327853228_sci_test_answer0724.json'
#     # 定义最终合并后输出文件的路径
#     output_path = 'D:\\ProJects\\kexue\\crawl_data\\0724\\merged_output\\merged_data0724.json'
#     # 调用我们上面定义的函数，并传入三个文件路径作为参数，来执行整个合并任务
#     merge_json_files(file_path_input, file_path_output, output_path)