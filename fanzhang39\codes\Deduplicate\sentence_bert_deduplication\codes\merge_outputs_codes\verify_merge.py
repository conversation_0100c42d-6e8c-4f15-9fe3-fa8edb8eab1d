#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证合并文件完整性脚本
对比原始JSON文件和合并后的文件，确保所有数据都被正确合并
"""

import json
import os
import glob
from typing import Dict, Set, List, Any
from collections import defaultdict


def load_original_files(input_dir: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    加载原始目录中的所有JSON文件（排除mapping文件）
    
    Args:
        input_dir: 输入目录路径
        
    Returns:
        字典，键为文件名，值为数据列表
    """
    original_data = {}
    
    # 获取所有JSON文件，排除mapping文件
    json_files = glob.glob(os.path.join(input_dir, "*.json"))
    json_files = [f for f in json_files if not f.endswith("_mapping.json")]
    
    print(f"找到 {len(json_files)} 个原始JSON文件:")
    
    total_records = 0
    for json_file in json_files:
        filename = os.path.basename(json_file)
        label_name = filename.replace('.json', '')
        print(f"  - 正在加载: {filename}")
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if isinstance(data, list):
                original_data[label_name] = data
                total_records += len(data)
                print(f"    加载了 {len(data)} 条记录")
            else:
                print(f"    警告: {filename} 不是列表格式，跳过")
                
        except Exception as e:
            print(f"    错误: 无法加载 {filename}: {e}")
    
    print(f"\n原始文件总记录数: {total_records}")
    return original_data


def load_merged_file(merged_file_path: str) -> List[Dict[str, Any]]:
    """
    加载合并后的文件
    
    Args:
        merged_file_path: 合并文件路径
        
    Returns:
        合并文件的数据列表
    """
    print(f"正在加载合并文件: {merged_file_path}")
    
    try:
        with open(merged_file_path, 'r', encoding='utf-8') as f:
            merged_data = json.load(f)
            
        if isinstance(merged_data, list):
            print(f"合并文件包含 {len(merged_data)} 条记录")
            return merged_data
        else:
            print("错误: 合并文件不是列表格式")
            return []
            
    except Exception as e:
        print(f"错误: 无法加载合并文件: {e}")
        return []


def create_content_sets(data_dict: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Set[str]]:
    """
    为每个文件创建content内容集合

    Args:
        data_dict: 数据字典

    Returns:
        content集合字典
    """
    content_sets = {}
    for label, data_list in data_dict.items():
        content_sets[label] = {item.get('content', '').strip() for item in data_list}
    return content_sets


def create_content_to_record_mapping(data_list: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    """
    创建content到完整记录的映射

    Args:
        data_list: 数据列表

    Returns:
        content到记录的映射字典
    """
    content_mapping = {}
    for item in data_list:
        content = item.get('content', '').strip()
        if content:
            content_mapping[content] = item
    return content_mapping


def verify_completeness(original_data: Dict[str, List[Dict[str, Any]]],
                       merged_data: List[Dict[str, Any]]) -> None:
    """
    验证合并文件的完整性（基于content内容对比）

    Args:
        original_data: 原始数据字典
        merged_data: 合并后的数据列表
    """
    print("\n" + "=" * 80)
    print("开始验证合并文件完整性（基于content内容对比）")
    print("=" * 80)

    # 1. 统计原始文件信息
    original_total = sum(len(data) for data in original_data.values())
    merged_total = len(merged_data)

    print(f"原始文件总记录数: {original_total}")
    print(f"合并文件总记录数: {merged_total}")

    # 2. 创建content集合用于对比
    print("\n正在创建content内容集合...")
    original_content_sets = create_content_sets(original_data)
    merged_content_set = {item.get('content', '').strip() for item in merged_data}

    # 3. 创建content到记录的映射，用于详细分析
    merged_content_mapping = create_content_to_record_mapping(merged_data)
    
    # 4. 统计各标签在合并文件中的content数量
    merged_label_content_counts = defaultdict(int)
    merged_label_contents = defaultdict(set)
    for item in merged_data:
        label = item.get('label', 'Unknown')
        content = item.get('content', '').strip()
        merged_label_content_counts[label] += 1
        merged_label_contents[label].add(content)

    # 5. 逐个标签验证content完整性
    print("\n" + "-" * 60)
    print("各标签content验证结果:")
    print("-" * 60)

    all_complete = True
    total_missing_contents = 0

    for label, original_contents in original_content_sets.items():
        original_content_count = len(original_contents)
        merged_content_count = len(merged_label_contents.get(label, set()))

        # 检查缺失的content
        missing_contents = original_contents - merged_content_set
        missing_content_count = len(missing_contents)

        status = "✓ 完整" if missing_content_count == 0 else f"✗ 缺失 {missing_content_count} 条content"

        print(f"{label}:")
        print(f"  原始content数: {original_content_count}")
        print(f"  合并content数: {merged_content_count}")
        print(f"  状态: {status}")

        if missing_content_count > 0:
            all_complete = False
            total_missing_contents += missing_content_count
            print(f"  缺失content (前5个):")
            for i, content in enumerate(list(missing_contents)[:5]):
                print(f"    {i+1}. {content}")
            if missing_content_count > 5:
                print(f"    ... 还有 {missing_content_count - 5} 个缺失content")

        print()
    
    # 6. 检查是否有多余的content
    all_original_contents = set()
    for contents in original_content_sets.values():
        all_original_contents.update(contents)

    extra_contents = merged_content_set - all_original_contents
    extra_content_count = len(extra_contents)

    # 7. 检查重复content
    merged_contents_list = [item.get('content', '').strip() for item in merged_data]
    duplicate_contents = []
    seen_contents = set()
    for content in merged_contents_list:
        if content and content in seen_contents:
            duplicate_contents.append(content)
        elif content:
            seen_contents.add(content)

    # 8. 检查content与label的匹配性
    print("-" * 60)
    print("content与label匹配性检查:")
    print("-" * 60)

    label_mismatch_count = 0
    for item in merged_data:
        content = item.get('content', '').strip()
        merged_label = item.get('label', '')

        # 查找这个content在原始数据中应该属于哪个label
        original_label = None
        for label, contents in original_content_sets.items():
            if content in contents:
                original_label = label
                break

        if original_label and original_label != merged_label:
            label_mismatch_count += 1
            if label_mismatch_count <= 5:  # 只显示前5个不匹配的例子
                print(f"  标签不匹配: '{content}' 原始标签='{original_label}' 合并标签='{merged_label}'")

    if label_mismatch_count > 5:
        print(f"  ... 还有 {label_mismatch_count - 5} 个标签不匹配的记录")
    elif label_mismatch_count == 0:
        print("  ✓ 所有content的标签都匹配正确")

    print()
    
    # 9. 输出最终结果
    print("=" * 80)
    print("验证结果总结（基于content内容对比）:")
    print("=" * 80)

    if (all_complete and extra_content_count == 0 and
        len(duplicate_contents) == 0 and label_mismatch_count == 0):
        print("✓ 合并文件完整且正确！")
        print("  - 所有原始content都已包含")
        print("  - 没有多余content")
        print("  - 没有重复content")
        print("  - 所有content的标签都正确")
    else:
        print("✗ 发现问题:")

        if total_missing_contents > 0:
            print(f"  - 缺失content: {total_missing_contents} 条")

        if extra_content_count > 0:
            print(f"  - 多余content: {extra_content_count} 条")
            print(f"    多余content (前3个):")
            for i, content in enumerate(list(extra_contents)[:3]):
                print(f"      {i+1}. {content}")

        if duplicate_contents:
            print(f"  - 重复content: {len(duplicate_contents)} 条")
            print(f"    重复content (前3个):")
            for i, content in enumerate(duplicate_contents[:3]):
                print(f"      {i+1}. {content}")

        if label_mismatch_count > 0:
            print(f"  - 标签不匹配: {label_mismatch_count} 条")

    print(f"\n统计信息:")
    print(f"  原始文件总content数: {len(all_original_contents)}")
    print(f"  合并文件总content数: {len(merged_content_set)}")
    print(f"  原始文件总记录数: {original_total}")
    print(f"  合并文件总记录数: {merged_total}")
    print(f"  content差异: {len(merged_content_set) - len(all_original_contents)}")
    print(f"  记录数差异: {merged_total - original_total}")

    print("=" * 80)


def main():
    """主函数"""
    # 路径配置
    original_dir = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/outputs/outputs_7_16/output_0.95"
    merged_file = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/datas/merged_datas/merged_datas_round1_0.95.json"
    
    print("=" * 80)
    print("合并文件完整性验证脚本")
    print("=" * 80)
    print(f"原始目录: {original_dir}")
    print(f"合并文件: {merged_file}")
    print("=" * 80)
    
    # 检查路径是否存在
    if not os.path.exists(original_dir):
        print(f"错误: 原始目录不存在: {original_dir}")
        return
    
    if not os.path.exists(merged_file):
        print(f"错误: 合并文件不存在: {merged_file}")
        return
    
    try:
        # 1. 加载原始文件
        original_data = load_original_files(original_dir)
        
        if not original_data:
            print("没有找到有效的原始数据，退出")
            return
        
        # 2. 加载合并文件
        merged_data = load_merged_file(merged_file)
        
        if not merged_data:
            print("没有找到有效的合并数据，退出")
            return
        
        # 3. 验证完整性
        verify_completeness(original_data, merged_data)
        
    except Exception as e:
        print(f"验证过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
