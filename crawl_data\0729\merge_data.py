"""
数据合并脚本
将sci_checkout0728.json和资源教育-语言学习_815_doubao-1.5-pro-32k-250115_周依凡_1753763656664_sci_extracted_prompt0729.json按照id进行合并
"""

import json
import os


def load_json_file(file_path):
    """加载JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def load_jsonl_file(file_path):
    """加载JSONL文件（每行一个JSON对象）"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                data.append(json.loads(line))
    return data


def parse_answer_field(answer_str):
    """解析answer字段中的JSON字符串"""
    try:
        # 解析JSON字符串
        answer_data = json.loads(answer_str)
        # 提取answer字段
        if 'answer' in answer_data:
            return answer_data['answer']
        else:
            return answer_data
    except (json.JSONDecodeError, TypeError):
        return {}


def merge_data(checkout_data, answer_data):
    """合并两个数据集"""
    # 创建answer数据的id映射
    answer_map = {}
    for item in answer_data:
        item_id = str(item.get('id', ''))
        if item_id:
            # 解析answer字段
            answer_content = parse_answer_field(item.get('answer', '{}'))
            if answer_content:
                answer_map[item_id] = answer_content

    # 合并数据
    merged_data = []

    for item in checkout_data:
        item_id = str(item.get('id', ''))

        # 创建新的合并对象
        merged_item = {
            "id": item.get("id", ""),
            "question": item.get("question", ""),
            "label": item.get("label", ""),
            "input": item.get("output", {}),  # 将原来的output改为input
        }

        # 如果找到对应的answer数据，添加answer字段
        if item_id in answer_map:
            merged_item["answer"] = answer_map[item_id]
        else:
            # 如果没有找到对应的answer，添加空的answer字段
            merged_item["answer"] = {
                "居里夫人_小行动": "",
                "居里夫人_冷知识": "",
                "达尔文_小行动": "",
                "达尔文_冷知识": ""
            }

        merged_data.append(merged_item)

    return merged_data


def save_merged_data(data, output_path):
    """保存合并后的数据"""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def main():
    """主函数"""
    # 定义文件路径
    checkout_file = r"sci_checkout0728.json"
    answer_file = r"03/资源教育-语言学习_887_doubao-1.5-pro-32k-250115_周依凡_1753786420304_sci_extracted_prompt0729.json"
    output_file = r"03/sci_answer.json"

    print(f"正在加载数据...")

    # 加载数据
    checkout_data = load_json_file(checkout_file)
    answer_data = load_jsonl_file(answer_file)

    print(f"sci_checkout0728.json: {len(checkout_data)} 条数据")
    print(f"answer文件: {len(answer_data)} 条数据")

    # 合并数据
    merged_data = merge_data(checkout_data, answer_data)

    print(f"合并后: {len(merged_data)} 条数据")

    # 保存合并后的数据
    save_merged_data(merged_data, output_file)

    print(f"合并完成，保存到: {output_file}")


if __name__ == "__main__":
    main()