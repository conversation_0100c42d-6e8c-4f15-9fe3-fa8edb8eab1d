import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys



def repace_yh(text):
    return text.replace("{'", '''{"''').replace(": '", ''': "''').replace("'}", '''"}''').replace("':", '''":''').replace(", '", ''', "''').replace("',", '''",''').strip()


def parse(line):
    try:
        data = json.loads(line)
        if not data['answer']:
            return None
        answer = repace_yh(data['answer'].split('```json')[-1].split(']')[0]) + ']'
        answer_lst = json.loads(answer)
        answer_id_set = set(d['id'] for d in answer_lst)

        if len(answer_id_set) != len(answer_lst) > 10:
            print(answer_id_set)
            return None

        if len(answer_lst) > 10:
            print(answer_lst)
            return None

        n, m = data['id'].split('-')
        question_id_lst = [i + int(n) * 10 for i in range(1, int(m) + 1)]
        question_id_set = set(question_id_lst)

        lst = []

        for i in range(len(answer_lst)):
            if answer_lst[i]['id'] not in question_id_set:
                continue

            if 'label' in answer_lst[i] and answer_lst[i]['label'] in '物理、化学、生物、地理、其他、非科学'.split('、'):
                lst.append(answer_lst[i])

    except Exception as e:
        print(e)
        print(answer)
        return None
    return lst


data_path = '/work1/data/fanzhang39/0624/res/cat.json'


id2label = {}
f = open(data_path, encoding='utf-8')
with Pool(64) as p:
    all_data = p.imap(parse, f)
    # print(len(all_data))
    for data in all_data:
        if data:
            for item in data:
                assert item['id'] not in id2label, str(item) + id2label[item['id']]
                id2label[item['id']] = item['label']

print(len(id2label))

save_file = '/work1/data/fanzhang39/0624/res/id2label.json'
with open(save_file, 'w', encoding='utf-8') as f:
    json.dump(id2label, f, ensure_ascii=False, indent=4)

# [{'id': 25951, 'label': '非科学'}, {'id': 25952, 'label': '化学'}, {'id': 25953, 'content': '太阳升为什么有黑子？', 'label': '其他'}, {'id': 25954, 'label': '其他'}, {'id': 25955, 'label': '其他'}, {'id': 25956, 'label': '非科学'}, {'id': 25957, 'label': '其他'}, {'id': 25958, 'label': '非科学'}, {'id': 25959, 'label': '非科学'}, {'id': 25960, 'label': '非科学'}]
# [{"id": 25951, "label": "非科学"}, {"id": 25952, "label": "化学"}, {"id": 25953, "content": "太阳升为什么有黑子？', "label": "其他"}, {"id": 25954, "label": "其他"}, {"id": 25955, "label": "其他"}, {"id": 25956, "label": "非科学"}, {"id": 25957, "label": "其他"}, {"id": 25958, "label": "非科学"}, {"id": 25959, "label": "非科学"}, {"id": 25960, "label": "非科学"}]
