import json


def extract_field(input_path,output_path):
    with open(input_path,'r',encoding='utf-8') as f:
        all_data = json.load(f)
    extracted_data = []
    for item in all_data:
        new_data = {
            "id": item.get('id'),
            "question": item.get('question'),
            "label": item.get('label'),
            "input": item.get('output')
        }
        extracted_data.append(new_data)

    
    with open(output_path,'w',encoding='utf-8') as f:
        json.dump(extracted_data,f,ensure_ascii=False,indent=4)

        




if __name__ == '__main__':
    input_path = "sci_checkout0728.json"
    output_path = "sci_extracted0729.json"
    extract_field(input_path, output_path)
