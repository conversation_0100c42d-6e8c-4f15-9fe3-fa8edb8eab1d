#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TF-IDF+余弦相似度去重算法的效果
验证合并规则是否符合预期
"""

import json
import os
import sys
from text_deduplication_tfidf import TextDeduplicationTFIDF


def create_comprehensive_test_data():
    """创建全面的测试数据，包含各种合并场景"""
    test_data = [
        # 组1：天空颜色相关（应该合并的）
        {"id": "1", "content": "为什么天空是蓝色的？", "label": "物理"},
        {"id": "2", "content": "天空为什么是蓝色的？", "label": "物理"},
        {"id": "3", "content": "天空为什么蓝？", "label": "物理"},
        
        # 组2：天空颜色询问（应该合并的）
        {"id": "4", "content": "天空什么颜色？", "label": "物理"},
        {"id": "5", "content": "天空是什么颜色的？", "label": "物理"},
        
        # 组3：不同动物条纹（不应该合并）
        {"id": "6", "content": "老虎为什么有条纹？", "label": "生物"},
        {"id": "7", "content": "斑马为什么有条纹？", "label": "生物"},
        
        # 组4：完全不同的生物问题（不应该合并）
        {"id": "8", "content": "为什么人会死？", "label": "生物"},
        {"id": "9", "content": "龟壳为什么那么硬？", "label": "生物"},
        {"id": "10", "content": "蛇为什么没有腿？", "label": "生物"},
        
        # 组5：地震相关（应该合并的）
        {"id": "11", "content": "为什么会发生地震？", "label": "地理"},
        {"id": "12", "content": "地震为什么会发生？", "label": "地理"},
        
        # 组6：不同天体（不应该合并）
        {"id": "13", "content": "太阳为什么发光？", "label": "物理"},
        {"id": "14", "content": "月亮为什么发光？", "label": "物理"},
        
        # 组7：水的状态（应该合并的）
        {"id": "15", "content": "水为什么会结冰？", "label": "物理"},
        {"id": "16", "content": "为什么水会结冰？", "label": "物理"},
        
        # 组8：不同植物（不应该合并）
        {"id": "17", "content": "玫瑰为什么有刺？", "label": "生物"},
        {"id": "18", "content": "仙人掌为什么有刺？", "label": "生物"},
        
        # 组9：重力相关（应该合并的）
        {"id": "19", "content": "为什么物体会下落？", "label": "物理"},
        {"id": "20", "content": "物体为什么会下落？", "label": "物理"},
    ]
    
    return test_data


def analyze_test_results(merge_mapping, original_data):
    """分析测试结果，验证合并是否符合预期，输出所有问题的详细日志"""
    print("\n=== 详细测试结果分析 ===")

    # 预期的合并组
    expected_merges = {
        "天空蓝色组": ["为什么天空是蓝色的？", "天空为什么是蓝色的？", "天空为什么蓝？"],
        "天空颜色组": ["天空什么颜色？", "天空是什么颜色的？"],
        "地震组": ["为什么会发生地震？", "地震为什么会发生？"],
        "水结冰组": ["水为什么会结冰？", "为什么水会结冰？"],
        "重力组": ["为什么物体会下落？", "物体为什么会下落？"]
    }

    # 不应该合并的问题
    should_not_merge = [
        "老虎为什么有条纹？", "斑马为什么有条纹？",
        "为什么人会死？", "龟壳为什么那么硬？", "蛇为什么没有腿？",
        "太阳为什么发光？", "月亮为什么发光？",
        "玫瑰为什么有刺？", "仙人掌为什么有刺？"
    ]

    print(f"原始数据总数: {len(original_data)} 条")
    print(f"合并群组数: {len(merge_mapping)} 个")

    # 创建所有问题的状态映射
    all_questions = {item['content']: item for item in original_data}
    merged_questions = set()  # 被合并的问题
    kept_questions = set()    # 被保留的问题

    # 收集合并信息
    for merge_key, merge_info in merge_mapping.items():
        kept_content = merge_info['kept_question']['content']
        kept_questions.add(kept_content)

        for merged_q in merge_info['merged_questions']:
            merged_questions.add(merged_q['content'])

    print(f"\n=== 所有问题状态详细日志 ===")

    # 输出每个问题的状态
    for i, item in enumerate(original_data, 1):
        content = item['content']
        question_id = item['id']
        label = item['label']

        if content in kept_questions:
            # 找到这个问题合并了哪些其他问题
            for merge_key, merge_info in merge_mapping.items():
                if merge_info['kept_question']['content'] == content:
                    merged_list = [q['content'] for q in merge_info['merged_questions']]
                    print(f"[{i:2d}] 🟢 保留: {content} (ID:{question_id}, 标签:{label})")
                    print(f"     └─ 合并了 {len(merged_list)} 个问题:")
                    for merged_content in merged_list:
                        print(f"        - {merged_content}")
                    break
        elif content in merged_questions:
            # 找到这个问题被合并到哪里
            for merge_key, merge_info in merge_mapping.items():
                for merged_q in merge_info['merged_questions']:
                    if merged_q['content'] == content:
                        kept_content = merge_info['kept_question']['content']
                        print(f"[{i:2d}] 🔴 合并: {content} (ID:{question_id}, 标签:{label})")
                        print(f"     └─ 被合并到: {kept_content}")
                        break
        else:
            print(f"[{i:2d}] ⚪ 独立: {content} (ID:{question_id}, 标签:{label})")

    print(f"\n=== 合并群组详细分析 ===")

    # 分析每个合并群组
    correct_merges = 0
    total_expected = len(expected_merges)

    for group_idx, (merge_key, merge_info) in enumerate(merge_mapping.items(), 1):
        kept_content = merge_info['kept_question']['content']
        merged_contents = [q['content'] for q in merge_info['merged_questions']]
        all_contents = [kept_content] + merged_contents

        print(f"\n群组 {group_idx}:")
        print(f"  保留问题: {kept_content}")
        print(f"  合并问题: {merged_contents}")

        # 检查是否符合预期
        is_expected = False
        for group_name, expected_group in expected_merges.items():
            if all(content in expected_group for content in all_contents) and len(all_contents) > 1:
                print(f"  ✅ 符合预期 ({group_name})")
                correct_merges += 1
                is_expected = True
                break

        if not is_expected:
            # 检查是否是错误合并
            is_incorrect = False
            for content in all_contents:
                if content in should_not_merge:
                    other_should_not_merge = [c for c in all_contents if c in should_not_merge and c != content]
                    if other_should_not_merge:
                        print(f"  ❌ 错误合并: 不应该合并的问题被合并在一起")
                        is_incorrect = True
                        break

            if not is_incorrect:
                print(f"  ⚠️  未预期的合并（可能是新发现的相似问题）")

    # 检查预期合并是否都实现了
    print(f"\n=== 预期合并检查 ===")
    for group_name, expected_group in expected_merges.items():
        found_merge = False
        for merge_info in merge_mapping.values():
            kept_content = merge_info['kept_question']['content']
            merged_contents = [q['content'] for q in merge_info['merged_questions']]
            all_contents = [kept_content] + merged_contents

            if all(content in expected_group for content in all_contents) and len(all_contents) > 1:
                found_merge = True
                break

        if found_merge:
            print(f"  ✅ {group_name}: 已正确合并")
        else:
            print(f"  ❌ {group_name}: 未能合并")

    # 统计独立问题
    independent_count = len(all_questions) - len(kept_questions) - len(merged_questions)

    # 总结
    print(f"\n=== 最终统计总结 ===")
    print(f"原始问题总数: {len(original_data)}")
    print(f"保留问题数: {len(kept_questions)}")
    print(f"被合并问题数: {len(merged_questions)}")
    print(f"独立问题数: {independent_count}")
    print(f"最终问题数: {len(kept_questions) + independent_count}")
    print(f"去重率: {len(merged_questions)/len(original_data)*100:.1f}%")
    print(f"预期合并组数: {total_expected}")
    print(f"正确合并组数: {correct_merges}")
    print(f"合并准确率: {correct_merges/total_expected*100:.1f}%")

    if correct_merges == total_expected:
        print("🎉 所有预期合并都已实现！")
        return True
    else:
        print("⚠️  部分预期合并未实现，需要调整参数")
        return False


def run_test():
    """运行完整测试"""
    print("=== 开始算法效果测试 ===")
    
    # 1. 创建测试数据
    test_data = create_comprehensive_test_data()
    test_file = "comprehensive_test_data.json"
    
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"创建测试数据: {len(test_data)} 条")
    
    # 2. 配置路径
    stopwords_path = r"/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/hagongda_stopwords.txt"
    output_dir = "test_output"
    
    # 3. 创建去重系统实例
    deduplicator = TextDeduplicationTFIDF(
        similarity_threshold=0.96,  # 基于通用语言学方法的优化阈值
        chunk_size=100             # 小块测试
    )
    
    try:
        # 4. 运行去重
        print("\n=== 运行去重算法 ===")
        deduplicator.run_deduplication(test_file, stopwords_path, output_dir)
        
        # 5. 加载结果
        mapping_file = os.path.join(output_dir, 'merge_mapping.json')
        if os.path.exists(mapping_file):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                merge_mapping = json.load(f)
            
            # 6. 分析结果
            success = analyze_test_results(merge_mapping, test_data)
            
            return success
        else:
            print("❌ 未找到合并映射文件")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)


def test_different_thresholds():
    """测试不同相似度阈值的效果"""
    print("\n=== 测试不同相似度阈值 ===")
    
    thresholds = [0.90, 0.95, 0.98]
    test_data = create_comprehensive_test_data()
    test_file = "threshold_test_data.json"
    
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    stopwords_path = r"/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/hagongda_stopwords.txt"
    
    for threshold in thresholds:
        print(f"\n--- 测试阈值: {threshold} ---")
        
        output_dir = f"test_output_threshold_{threshold}"
        deduplicator = TextDeduplicationTFIDF(
            similarity_threshold=threshold,
            chunk_size=100
        )
        
        try:
            deduplicator.run_deduplication(test_file, stopwords_path, output_dir)
            
            # 读取结果
            mapping_file = os.path.join(output_dir, 'merge_mapping.json')
            if os.path.exists(mapping_file):
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    merge_mapping = json.load(f)
                
                print(f"阈值 {threshold}: 合并了 {len(merge_mapping)} 个群组")
            
        except Exception as e:
            print(f"阈值 {threshold} 测试失败: {e}")
    
    # 清理
    if os.path.exists(test_file):
        os.remove(test_file)


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "threshold":
        # 阈值测试模式
        test_different_thresholds()
    else:
        # 标准测试模式
        success = run_test()
        if success:
            print("\n🎉 所有测试通过！")
            sys.exit(0)
        else:
            print("\n⚠️  测试未完全通过")
            sys.exit(1)
