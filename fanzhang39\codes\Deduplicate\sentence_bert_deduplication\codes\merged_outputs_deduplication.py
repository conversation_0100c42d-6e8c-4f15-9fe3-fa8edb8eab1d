"""
BGE-M3文本去重合并系统
基于BGE-M3模型的语义相似度文本去重
"""

# 解决TLS冲突问题 - 必须在导入其他库之前设置
import os
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'

import json
import numpy as np
import torch
import torch.nn.functional as F
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Tuple, Any
import logging
from datetime import datetime
import argparse
from config import Config
from pathlib import Path
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bge_m3_deduplication.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BGE_M3_Deduplicator:
    """BGE-M3文本去重器"""
    
    def __init__(self, model_path: str, device: str = None):
        """
        初始化去重器
        """
        self.model_path = model_path
        self.device = device or self._select_best_device()
        self.model = None
        self.batch_size = 64  
        self.max_length = 512

        logger.info(f"初始化BGE-M3去重器，设备: {self.device}")
        self._log_gpu_status()
        self._load_model()

    def _select_best_device(self):
        """选择最优GPU设备 - 强制使用GPU1"""
        if not torch.cuda.is_available():
            raise RuntimeError("CUDA不可用，但配置为GPU-only模式")

        # 强制使用GPU1
        target_gpu_id = 1

        if target_gpu_id >= torch.cuda.device_count():
            raise RuntimeError(f"GPU {target_gpu_id} 不存在，系统只有 {torch.cuda.device_count()} 个GPU")

        try:
            # 检查GPU1的状态
            torch.cuda.set_device(target_gpu_id)
            total_memory = torch.cuda.get_device_properties(target_gpu_id).total_memory
            allocated_memory = torch.cuda.memory_allocated(target_gpu_id)
            free_memory = total_memory - allocated_memory
            usage_percent = (allocated_memory / total_memory) * 100

            logger.info(f"强制使用GPU {target_gpu_id}: {usage_percent:.1f}% 已使用, {free_memory/1024**3:.1f}GB 可用")

            # 无论内存状况如何，都使用GPU1
            if free_memory < 1 * 1024**3:  # 如果内存不足，给出警告但仍然使用
                logger.warning(f"GPU {target_gpu_id} 可用内存较少 ({free_memory/1024**3:.1f}GB)，但仍强制使用")

            return f"cuda:{target_gpu_id}"

        except Exception as e:
            logger.error(f"检查GPU {target_gpu_id}时出错: {e}")
            raise RuntimeError(f"无法使用GPU {target_gpu_id}: {e}")

    def _log_gpu_status(self):
        """记录GPU状态信息"""
        if not torch.cuda.is_available():
            logger.info("CUDA不可用，将使用CPU")
            return

        logger.info(f"检测到 {torch.cuda.device_count()} 个GPU:")
        for i in range(torch.cuda.device_count()):
            try:
                props = torch.cuda.get_device_properties(i)
                total_memory = props.total_memory / 1024**3
                allocated_memory = torch.cuda.memory_allocated(i) / 1024**3
                usage_percent = (allocated_memory / total_memory) * 100
                logger.info(f"  GPU {i}: {props.name}, {total_memory:.1f}GB, {usage_percent:.1f}% 已使用")
            except:
                logger.warning(f"  GPU {i}: 无法获取信息")

    def _load_model(self):
        """加载BGE-M3模型"""
        try:
            logger.info(f"正在加载BGE-M3模型: {self.model_path}")

            # 如果使用GPU，先设置设备
            if self.device.startswith("cuda:"):
                device_id = int(self.device.split(":")[1])
                torch.cuda.set_device(device_id)
                logger.info(f"设置CUDA设备: {device_id}")

            self.model = SentenceTransformer(
                self.model_path,
                device=self.device
            )
            # 设置最大序列长度
            self.model.max_seq_length = self.max_length

            logger.info("BGE-M3模型加载成功")

        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            # 尝试回退到CPU
            if self.device != "cpu":
                logger.info("尝试回退到CPU...")
                self.device = "cpu"
                self._load_model()
            else:
                raise
    
    def _preprocess_text(self, text: str) -> str:
        """
        文本预处理
        
        Args:
            text: 原始文本
            
        Returns:
            处理后的文本
        """
        if not text:
            return ""
        
        # 基础清理
        text = text.strip()
        # 保留中文标点，去除多余空格
        text = ' '.join(text.split())
        
        return text
    
    def encode_texts(self, texts: List[str]) -> np.ndarray:
        """批量编码文本为向量"""
        if not texts:
            return np.array([])
        
        # 预处理文本
        processed_texts = [self._preprocess_text(text) for text in texts]
        
        logger.info(f"正在编码 {len(texts)} 个文本...")
        
        try:
            # 批量编码
            embeddings = self.model.encode(
                processed_texts,
                batch_size=self.batch_size,
                show_progress_bar=True,
                convert_to_numpy=True,
                normalize_embeddings=True  # L2归一化
            )
            
            logger.info(f"文本编码完成，向量维度: {embeddings.shape}")
            return embeddings
            
        except Exception as e:
            logger.error(f"文本编码失败: {e}")
            raise
    
    def find_similar_pairs(self, embeddings: np.ndarray, threshold: float) -> List[Tuple[int, int, float]]:
        """查找相似文本对 - GPU-only处理策略"""
        logger.info(f"计算相似度矩阵，阈值: {threshold}")

        n = len(embeddings)

        # 逐行计算配置
        batch_size = Config.ROWWISE_BATCH_SIZE  # 从配置获取批处理大小

        # 使用GPU处理
        device = self.device if self.device.startswith("cuda") else f"cuda:{Config.DEFAULT_GPU_ID}"

        logger.info(f"处理 {n} 个问题，使用设备: {device}")

        # 转换为PyTorch张量并L2归一化
        embeddings_tensor = torch.tensor(embeddings, dtype=torch.float32, device=device)
        embeddings_normalized = F.normalize(embeddings_tensor, p=2, dim=1)

        # 初始化一个空列表，用于存储找到的相似文本对。
        similar_pairs = []

        # 逐批处理
        for i in range(0, n, batch_size):
            # 到最后剩不到100防止越界
            end_i = min(i + batch_size, n)

            # 当前批次
            batch_embeddings = embeddings_normalized[i:end_i]

            # 计算当前批次与所有向量的相似度（矩阵乘法）
            similarities = torch.mm(batch_embeddings, embeddings_normalized.t())

            # GPU上进行阈值筛选和索引提取
            batch_size_actual = end_i - i
            for local_i in range(batch_size_actual):
                global_i = i + local_i
                # 只比较后面的问题，避免重复
                if global_i + 1 < n:
                    # 获取当前行的相似度
                    row_similarities = similarities[local_i, global_i + 1:]
                    # 找到超过阈值的位置
                    mask = row_similarities >= threshold
                    if mask.any():
                        # 获取索引和相似度值
                        indices = torch.nonzero(mask, as_tuple=True)[0]
                        values = row_similarities[mask]
                        # 转换为全局索引并添加到结果
                        for idx, val in zip(indices, values):
                            j = global_i + 1 + idx.item()
                            similar_pairs.append((global_i, j, float(val.item())))

            # 清理批次内存
            del similarities
            torch.cuda.empty_cache()

            # 显示进度
            if i % (batch_size * 10) == 0 or end_i == n:
                progress = (end_i / n) * 100
                logger.info(f"进度: {end_i}/{n} ({progress:.1f}%), 已找到 {len(similar_pairs)} 个相似对")

        # 清理总内存
        del embeddings_tensor, embeddings_normalized
        torch.cuda.empty_cache()

        logger.info(f"GPU-only计算完成，总共找到 {len(similar_pairs)} 个相似对")
        return similar_pairs



    def merge_questions(self, questions: List[Dict], similar_pairs: List[Tuple[int, int, float]]) -> Tuple[List[Dict], Dict]:
        """合并相似问题 - GPU加速版本"""
        logger.info("开始合并相似问题...")

        # 记录开始时间
        start_merge_time = time.time()

        # 使用当前设备进行合并处理
        merge_device = self.device

        try:
            # 设置GPU设备
            if merge_device.startswith("cuda:"):
                device_id = int(merge_device.split(":")[1])
                torch.cuda.set_device(device_id)
                logger.info(f"使用 {merge_device} 进行合并处理")

            # 将相似对转换为GPU张量进行并行处理
            if similar_pairs:
                # 提取索引对和相似度
                pairs_array = np.array(similar_pairs)
                idx1_tensor = torch.tensor(pairs_array[:, 0].astype(int), dtype=torch.long, device=merge_device)
                idx2_tensor = torch.tensor(pairs_array[:, 1].astype(int), dtype=torch.long, device=merge_device)

                # 在GPU上构建问题ID映射，确保ID为数值类型
                question_ids_list = []
                for q in questions:
                    qid = q['id']
                    # 处理可能的字符串ID
                    if isinstance(qid, str):
                        try:
                            qid = int(qid)
                        except ValueError:
                            qid = hash(qid) % (2**31)  # 转换为数值
                    question_ids_list.append(int(qid))

                question_ids = torch.tensor(question_ids_list, dtype=torch.long, device=merge_device)

                # GPU并行选择代表问题（ID较小的作为代表）
                id1_values = question_ids[idx1_tensor]
                id2_values = question_ids[idx2_tensor]

                # 选择ID较小的作为代表
                representatives = torch.where(id1_values <= id2_values, idx1_tensor, idx2_tensor)
                to_merge_indices = torch.where(id1_values <= id2_values, idx2_tensor, idx1_tensor)

                # 转回CPU进行群组构建（保持原有逻辑）
                representatives_cpu = representatives.cpu().numpy()
                to_merge_cpu = to_merge_indices.cpu().numpy()

                # 清理GPU内存
                del idx1_tensor, idx2_tensor, question_ids, id1_values, id2_values, representatives, to_merge_indices
                torch.cuda.empty_cache()

                logger.info(f"GPU处理完成，转回CPU构建 {len(representatives_cpu)} 个合并关系")
            else:
                representatives_cpu = np.array([])
                to_merge_cpu = np.array([])

        except Exception as e:
            logger.warning(f"GPU处理失败，回退到CPU: {e}")
            # 回退到原始CPU处理
            representatives_cpu = []
            to_merge_cpu = []
            for idx1, idx2, similarity in similar_pairs:
                if questions[idx1]['id'] <= questions[idx2]['id']:
                    representatives_cpu.append(idx1)
                    to_merge_cpu.append(idx2)
                else:
                    representatives_cpu.append(idx2)
                    to_merge_cpu.append(idx1)
            representatives_cpu = np.array(representatives_cpu)
            to_merge_cpu = np.array(to_merge_cpu)

        # 使用字典方法构建合并群组
        total_pairs = len(representatives_cpu)
        logger.info(f"使用字典方法构建合并群组... (处理 {total_pairs:,} 个相似对)")

        # 构建合并群组（使用原有的正确CPU逻辑）
        merge_groups = {}  # {代表问题索引: [合并的问题索引列表]}
        merged_indices = set()  # 已被合并的问题索引

        # 进度显示配置
        progress_interval = max(1, total_pairs // 100)  # 每1%显示一次进度
        last_progress_time = time.time()

        # 处理GPU处理后的结果
        for idx, (representative, to_merge) in enumerate(zip(representatives_cpu, to_merge_cpu)):
            representative, to_merge = int(representative), int(to_merge)

            # 如果代表问题已经在某个群组中，使用该群组的代表
            found_group = None
            for group_rep, group_members in merge_groups.items():
                if representative in group_members or representative == group_rep:
                    found_group = group_rep
                    break

            if found_group is not None:
                representative = found_group

            # 初始化群组
            if representative not in merge_groups:
                merge_groups[representative] = []

            # 添加到合并列表
            if to_merge not in merge_groups[representative] and to_merge != representative:
                merge_groups[representative].append(to_merge)
                merged_indices.add(to_merge)

            # 显示进度
            if idx % progress_interval == 0 or idx == total_pairs - 1:
                current_time = time.time()
                progress_percent = (idx + 1) / total_pairs * 100
                elapsed_time = current_time - last_progress_time

                if idx > 0:
                    estimated_total_time = (current_time - start_merge_time) / (idx + 1) * total_pairs
                    remaining_time = estimated_total_time - (current_time - start_merge_time)

                    logger.info(f"构建进度: {idx+1:,}/{total_pairs:,} ({progress_percent:.1f}%) "
                              f"| 当前群组数: {len(merge_groups):,} "
                              f"| 已合并问题: {len(merged_indices):,} "
                              f"| 预计剩余: {remaining_time:.1f}秒")
                else:
                    start_merge_time = current_time
                    logger.info(f"构建进度: {idx+1:,}/{total_pairs:,} ({progress_percent:.1f}%) "
                              f"| 当前群组数: {len(merge_groups):,} "
                              f"| 已合并问题: {len(merged_indices):,}")

                last_progress_time = current_time

        # 完成群组构建的统计
        end_merge_time = time.time()
        total_merge_time = end_merge_time - start_merge_time
        logger.info(f"群组构建完成! 耗时: {total_merge_time:.2f}秒")
        logger.info(f"最终统计: {len(merge_groups):,} 个合并群组, {len(merged_indices):,} 个问题将被合并")

        # 生成去重后的问题列表
        logger.info("生成最终去重结果...")
        deduplicated_questions = []
        mapping = {}

        for i, question in enumerate(questions):
            if i not in merged_indices:
                deduplicated_questions.append(question)

                # 如果这个问题是某个群组的代表，记录映射
                if i in merge_groups:
                    merged_questions = [questions[j] for j in merge_groups[i]]
                    mapping[question['id']] = {
                        'kept_question': question,
                        'merged_questions': merged_questions,
                        'merge_count': len(merged_questions)
                    }

        final_time = time.time()
        total_process_time = final_time - start_merge_time
        logger.info(f"合并完成: {len(questions):,} -> {len(deduplicated_questions):,} (减少 {len(questions) - len(deduplicated_questions):,} 个)")
        logger.info(f"总处理时间: {total_process_time:.2f}秒")
        return deduplicated_questions, mapping

    def _cpu_union_find(self, representatives_cpu: np.ndarray, to_merge_cpu: np.ndarray, num_nodes: int) -> np.ndarray:
        """CPU并查集算法作为GPU方案的回退"""
        parent = np.arange(num_nodes)
        rank = np.zeros(num_nodes)

        def find(x):
            if parent[x] != x:
                parent[x] = find(parent[x])  # 路径压缩
            return parent[x]

        def union(x, y):
            root_x = find(x)
            root_y = find(y)

            if root_x != root_y:
                # 按秩合并
                if rank[root_x] < rank[root_y]:
                    parent[root_x] = root_y
                elif rank[root_x] > rank[root_y]:
                    parent[root_y] = root_x
                else:
                    parent[root_y] = root_x
                    rank[root_x] += 1

        # 处理所有边
        for rep, merge in zip(representatives_cpu, to_merge_cpu):
            union(int(rep), int(merge))

        # 最终路径压缩
        for i in range(num_nodes):
            find(i)

        return parent
    
    def process_file(self, input_file: str, output_dir: str, thresholds: List[float]):
        """处理合并数据文件"""
        logger.info(f"=== 开始处理合并数据文件 ===")
        logger.info(f"输入文件: {input_file}")

        # 读取合并后的数据
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                questions = json.load(f)
            logger.info(f"成功加载 {len(questions)} 个问题")

            # 统计各标签的数量
            label_counts = {}
            for q in questions:
                label = q.get('label', 'Unknown')
                label_counts[label] = label_counts.get(label, 0) + 1

            logger.info("各标签问题数量:")
            for label, count in sorted(label_counts.items()):
                logger.info(f"  {label}: {count}")

        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return

        if not questions:
            logger.warning("文件为空，跳过处理")
            return

        # 提取文本内容
        texts = [q.get('content', '') for q in questions]
        logger.info(f"提取了 {len(texts)} 个文本内容")

        # 编码文本
        logger.info("开始文本编码...")
        embeddings = self.encode_texts(texts)

        # 获取文件名（不含扩展名）
        file_name = Path(input_file).stem

        # 对每个阈值进行处理
        for threshold in thresholds:
            logger.info(f"=== 处理相似度阈值: {threshold} ===")

            # 创建输出目录
            threshold_dir = os.path.join(output_dir, f"output_{threshold}")
            os.makedirs(threshold_dir, exist_ok=True)
            logger.info(f"输出目录: {threshold_dir}")

            # 查找相似对
            logger.info("开始查找相似文本对...")
            similar_pairs = self.find_similar_pairs(embeddings, threshold)

            # 合并问题
            logger.info("开始合并相似问题...")
            deduplicated_questions, mapping = self.merge_questions(questions, similar_pairs)

            # 保存去重后的问题
            output_file = os.path.join(threshold_dir, f"{file_name}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(deduplicated_questions, f, ensure_ascii=False, indent=2)
            logger.info(f"去重结果已保存: {output_file}")

            # 保存映射文件
            if mapping:
                mapping_file = os.path.join(threshold_dir, f"{file_name}_mapping.json")
                with open(mapping_file, 'w', encoding='utf-8') as f:
                    json.dump(mapping, f, ensure_ascii=False, indent=2)
                logger.info(f"映射文件已保存: {mapping_file}")

            # 生成统计报告
            self._generate_report(
                questions, deduplicated_questions, mapping,
                threshold, threshold_dir, file_name
            )

            logger.info(f"=== 阈值 {threshold} 处理完成 ===")
            logger.info(f"原始问题数: {len(questions)}")
            logger.info(f"去重后数量: {len(deduplicated_questions)}")
            logger.info(f"合并问题数: {len(questions) - len(deduplicated_questions)}")
            logger.info(f"合并比例: {((len(questions) - len(deduplicated_questions)) / len(questions) * 100):.2f}%")
    
    def _generate_report(self, original_questions: List[Dict], deduplicated_questions: List[Dict], 
                        mapping: Dict, threshold: float, output_dir: str, file_name: str):
        """生成统计报告"""
        report_file = os.path.join(output_dir, f"{file_name}_report.txt")
        
        original_count = len(original_questions)
        deduplicated_count = len(deduplicated_questions)
        merged_count = original_count - deduplicated_count
        merge_ratio = (merged_count / original_count * 100) if original_count > 0 else 0
        
        # 计算合并群组统计
        merge_groups = list(mapping.values())
        max_merge_size = max([group['merge_count'] for group in merge_groups]) if merge_groups else 0
        avg_merge_size = np.mean([group['merge_count'] for group in merge_groups]) if merge_groups else 0
        
        # 统计各标签的去重情况
        original_label_counts = {}
        deduplicated_label_counts = {}

        for q in original_questions:
            label = q.get('label', 'Unknown')
            original_label_counts[label] = original_label_counts.get(label, 0) + 1

        for q in deduplicated_questions:
            label = q.get('label', 'Unknown')
            deduplicated_label_counts[label] = deduplicated_label_counts.get(label, 0) + 1

        report_content = f"""BGE-M3合并数据去重统计报告
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
文件名: {file_name}
相似度阈值: {threshold}
处理设备: GPU1 (cuda:1)

=== 总体去重统计 ===
原始问题数量: {original_count}
去重后数量: {deduplicated_count}
合并问题数量: {merged_count}
合并比例: {merge_ratio:.2f}%

=== 各标签去重统计 ==="""

        # 添加各标签的详细统计
        for label in sorted(original_label_counts.keys()):
            orig_count = original_label_counts.get(label, 0)
            dedup_count = deduplicated_label_counts.get(label, 0)
            label_merged = orig_count - dedup_count
            label_ratio = (label_merged / orig_count * 100) if orig_count > 0 else 0

            report_content += f"""
{label}:
  原始数量: {orig_count}
  去重后数量: {dedup_count}
  合并数量: {label_merged}
  合并比例: {label_ratio:.2f}%"""

        report_content += f"""

=== 合并群组统计 ===
合并群组数量: {len(merge_groups)}
最大合并群组大小: {max_merge_size}
平均合并群组大小: {avg_merge_size:.2f}

=== 详细合并信息 ===
"""
        
        # 添加每个合并群组的详细信息
        for group_id, group_info in mapping.items():
            kept_question = group_info['kept_question']
            merged_questions = group_info['merged_questions']
            
            report_content += f"\n群组 {group_id} (保留):\n"
            report_content += f"  内容: {kept_question['content']}\n"
            report_content += f"  合并了 {len(merged_questions)} 个问题:\n"
            
            for merged_q in merged_questions:
                report_content += f"    - ID {merged_q['id']}: {merged_q['content']}\n"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"统计报告已保存: {report_file}")

def main():
    """主函数 - 专门处理合并后的数据文件"""
    parser = argparse.ArgumentParser(description='BGE-M3合并数据去重系统')
    parser.add_argument('--model_path', type=str,
                       default='/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/model',
                       help='BGE-M3模型路径')
    parser.add_argument('--input_file', type=str,
                       default='/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/datas/merged_datas/merged_datas_round1_0.95.json',
                       help='输入的合并数据文件')
    parser.add_argument('--output_path', type=str,
                       default='/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/outputs/merged_outputs',
                       help='输出目录')
    parser.add_argument('--thresholds', type=float, nargs='+',
                       default=[0.95],
                       help='相似度阈值列表')
    parser.add_argument('--device', type=str, default='cuda:1',
                       help='计算设备 (强制使用GPU1)')

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        logger.error(f"输入文件不存在: {args.input_file}")
        return

    logger.info(f"=== BGE-M3合并数据去重系统 ===")
    logger.info(f"输入文件: {args.input_file}")
    logger.info(f"输出目录: {args.output_path}")
    logger.info(f"使用设备: {args.device}")
    logger.info(f"相似度阈值: {args.thresholds}")

    # 强制使用GPU1创建去重器
    deduplicator = BGE_M3_Deduplicator(args.model_path, device='cuda:1')

    # 创建输出目录
    os.makedirs(args.output_path, exist_ok=True)

    # 处理合并后的数据文件
    logger.info("开始处理合并数据文件...")
    deduplicator.process_file(args.input_file, args.output_path, args.thresholds)

    logger.info("合并数据去重处理完成！")

class MultiGPU_BGE_M3_Processor:
    """多GPU并行文件处理器"""

    def __init__(self, model_path: str, num_gpus: int = None, gpu_ids: List[int] = None):
        self.model_path = model_path
        self.num_gpus = num_gpus or torch.cuda.device_count()
        # {{ AURA-X: Fix - 修复GPU分配逻辑，使用GPU ID列表. Approval: 寸止(ID:20250716). }}
        self.gpu_ids = gpu_ids or list(range(self.num_gpus))  # 使用指定的GPU ID列表
        self.logger = logging.getLogger(__name__)

        if self.num_gpus == 0:
            raise RuntimeError("没有可用的GPU设备")

        # 验证GPU ID的有效性
        max_gpu_id = torch.cuda.device_count() - 1
        valid_gpu_ids = [gpu_id for gpu_id in self.gpu_ids if 0 <= gpu_id <= max_gpu_id]

        if len(valid_gpu_ids) != len(self.gpu_ids):
            invalid_ids = [gpu_id for gpu_id in self.gpu_ids if gpu_id not in valid_gpu_ids]
            self.logger.warning(f"无效的GPU ID: {invalid_ids}，将被忽略")
            self.gpu_ids = valid_gpu_ids
            self.num_gpus = len(self.gpu_ids)

        self.logger.info(f"初始化多GPU处理器，使用 {self.num_gpus} 个GPU: {self.gpu_ids}")

    def get_file_sizes(self, input_path: str) -> List[Tuple[str, int]]:
        """获取所有JSON文件及其大小"""
        import json
        from config import Config

        files_with_size = []

        # 如果配置了TARGET_FILES，只处理指定文件
        if hasattr(Config, 'TARGET_FILES') and Config.TARGET_FILES:
            target_files = Config.TARGET_FILES
            self.logger.info(f"只处理指定的 {len(target_files)} 个文件: {target_files}")
        else:
            # 否则处理所有JSON文件
            target_files = [f for f in os.listdir(input_path) if f.endswith('.json')]

        for file_name in target_files:
            file_path = os.path.join(input_path, file_name)
            if not os.path.exists(file_path):
                self.logger.warning(f"文件不存在: {file_path}")
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    size = len(data) if isinstance(data, list) else 1
                files_with_size.append((file_path, size))
                self.logger.info(f"文件 {file_name}: {size} 个问题")
            except Exception as e:
                self.logger.warning(f"无法读取文件 {file_name}: {e}")
                files_with_size.append((file_path, 0))

        # 按大小排序，大文件在前
        files_with_size.sort(key=lambda x: x[1], reverse=True)
        return files_with_size

    def allocate_files_to_gpus(self, files_with_size: List[Tuple[str, int]]) -> List[Tuple[int, List[str]]]:
        """智能分配文件到GPU - 使用指定的GPU ID列表"""
        # {{ AURA-X: Fix - 修复GPU分配逻辑，使用正确的GPU ID映射. Approval: 寸止(ID:20250716). }}
        gpu_assignments = [[] for _ in range(self.num_gpus)]
        gpu_loads = [0] * self.num_gpus

        # 贪心算法：总是分配给当前负载最轻的GPU
        for file_path, size in files_with_size:
            min_gpu_idx = gpu_loads.index(min(gpu_loads))
            gpu_assignments[min_gpu_idx].append(file_path)
            gpu_loads[min_gpu_idx] += size

            # 使用正确的GPU ID
            actual_gpu_id = self.gpu_ids[min_gpu_idx]
            file_name = os.path.basename(file_path)
            self.logger.info(f"分配 {file_name} ({size}个问题) 到 GPU {actual_gpu_id}")

        # 显示最终分配结果并返回实际GPU ID
        result = []
        for gpu_idx in range(self.num_gpus):
            actual_gpu_id = self.gpu_ids[gpu_idx]
            file_names = [os.path.basename(f) for f in gpu_assignments[gpu_idx]]
            self.logger.info(f"GPU {actual_gpu_id}: {len(gpu_assignments[gpu_idx])} 个文件, 总负载: {gpu_loads[gpu_idx]} 个问题")
            self.logger.info(f"  文件: {file_names}")

            if gpu_assignments[gpu_idx]:  # 只返回有文件分配的GPU
                result.append((actual_gpu_id, gpu_assignments[gpu_idx]))

        return result

    def process_files_on_gpu(self, gpu_id: int, file_list: List[str], output_dir: str, thresholds: List[float]):
        """在指定GPU上处理文件列表"""
        try:
            # 设置当前进程使用的GPU
            torch.cuda.set_device(gpu_id)

            self.logger.info(f"GPU {gpu_id} 开始处理 {len(file_list)} 个文件")

            # 创建去重器
            deduplicator = BGE_M3_Deduplicator(
                model_path=self.model_path,
                device=f"cuda:{gpu_id}"
            )

            # 处理分配的文件
            for file_path in file_list:
                file_name = os.path.basename(file_path)
                self.logger.info(f"GPU {gpu_id} 正在处理: {file_name}")

                try:
                    deduplicator.process_file(file_path, output_dir, thresholds)
                    self.logger.info(f"GPU {gpu_id} 完成: {file_name}")
                except Exception as e:
                    self.logger.error(f"GPU {gpu_id} 处理 {file_name} 失败: {e}")

            self.logger.info(f"GPU {gpu_id} 所有文件处理完成")

        except Exception as e:
            self.logger.error(f"GPU {gpu_id} 处理过程出错: {e}")
            raise

    def process_all_files_parallel(self, input_path: str, output_dir: str, thresholds: List[float]):
        """并行处理所有文件"""
        import multiprocessing as mp
        from concurrent.futures import ProcessPoolExecutor
        import time

        # 确保使用spawn方式启动进程（解决CUDA fork问题）
        try:
            mp.set_start_method('spawn', force=True)
        except RuntimeError:
            # 如果已经设置过，忽略错误
            pass

        start_time = time.time()

        # 获取文件大小并分配
        files_with_size = self.get_file_sizes(input_path)
        if not files_with_size:
            self.logger.warning("没有找到JSON文件")
            return

        gpu_assignments = self.allocate_files_to_gpus(files_with_size)

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 并行处理
        self.logger.info("开始多GPU并行处理...")

        # {{ AURA-X: Modify - 适应新的GPU分配格式. Approval: 寸止(ID:20250716). }}
        with ProcessPoolExecutor(max_workers=len(gpu_assignments)) as executor:
            futures = []

            for actual_gpu_id, file_list in gpu_assignments:
                future = executor.submit(
                    self.process_files_on_gpu,
                    actual_gpu_id,
                    file_list,
                    output_dir,
                    thresholds
                )
                futures.append((actual_gpu_id, future))

            # 等待所有GPU完成
            for gpu_id, future in futures:
                try:
                    future.result()
                    self.logger.info(f"GPU {gpu_id} 任务完成")
                except Exception as e:
                    self.logger.error(f"GPU {gpu_id} 任务失败: {e}")

        end_time = time.time()
        total_time = end_time - start_time

        self.logger.info(f"=== 多GPU并行处理完成 ===")
        self.logger.info(f"总处理时间: {total_time:.2f} 秒")
        self.logger.info(f"处理文件数: {len(files_with_size)}")
        self.logger.info(f"使用GPU数: {self.num_gpus}")

if __name__ == "__main__":
    main()
