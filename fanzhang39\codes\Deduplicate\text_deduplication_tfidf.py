import json
import os
import sys
import time
import logging
from typing import List, Dict, Tuple, Set
from collections import defaultdict
import numpy as np
import pandas as pd
from scipy.sparse import csr_matrix
from scipy.sparse.csgraph import connected_components
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba


class TextDeduplicationTFIDF:
    
    def __init__(self, similarity_threshold: float = 0.96, chunk_size: int = 50000):
        """
        初始化去重系统
        """
        self.similarity_threshold = similarity_threshold
        self.chunk_size = chunk_size
        self.stopwords = set()
        self.vectorizer = None
        self.merge_mapping = {}  # 存储合并映射关系
        self.statistics = {
            'original_count': 0,
            'deduplicated_count': 0,
            'merged_groups': 0,
            'processing_time': 0
        }

        self.setup_logging()

        self.setup_vectorizer()
    
    def setup_logging(self):
        """配置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('processing_log.txt', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_vectorizer(self):
        """
        配置TF-IDF向量化器
        """
        self.vectorizer = TfidfVectorizer(
            max_features=50000,      # 最大特征数
            min_df=1,                
            max_df=0.7,              
            ngram_range=(1, 2),      
            lowercase=True,          # 转小写
            token_pattern=r'[\u4e00-\u9fff]+',  # 只保留中文字符
            stop_words=None,        
            sublinear_tf=True,       # 使用对数TF缩放，减少高频词主导
            norm='l2',               # L2标准化
            use_idf=True,            # 确保使用IDF权重
            smooth_idf=True          # 平滑IDF，避免除零
        )
    
    def load_stopwords(self, stopwords_path: str) -> None:
        """
        加载哈工大停用词表
        
        Args:
            stopwords_path: 停用词文件路径
        """
        try:
            with open(stopwords_path, 'r', encoding='utf-8') as f:
                self.stopwords = set(line.strip() for line in f if line.strip())
            self.logger.info(f"成功加载 {len(self.stopwords)} 个停用词")
        except Exception as e:
            self.logger.error(f"加载停用词失败: {e}")
            self.stopwords = set()
    
    def preprocess_text(self, text: str) -> str:
        """
        基于通用语言学原理的中文文本预处理

        使用词长度、字符特征等通用方法识别重要词汇，
        避免硬编码特定领域词汇，确保算法泛化能力

        Args:
            text: 原始文本

        Returns:
            处理后的文本
        """
        if not text or not isinstance(text, str):
            return ""

        words = jieba.cut(text.strip())

        filtered_words = []
        for word in words:
            word = word.strip()

            if (len(word) >= 1 and
                word not in self.stopwords and
                '\u4e00' <= word[0] <= '\u9fff'):

                importance_weight = self._calculate_word_importance(word)

                if importance_weight >= 3:
                    filtered_words.extend([word] * 3)
                elif importance_weight >= 2:
                    filtered_words.extend([word] * 2)
                elif len(word) > 1:
                    filtered_words.append(word)

        return ' '.join(filtered_words)

    def _calculate_word_importance(self, word: str) -> int:
        """
        基于通用语言学特征计算词汇重要性

        使用词长度、字符特征、语言学模式等通用方法，
        避免依赖特定领域知识

        Args:
            word: 待评估的词汇

        Returns:
            重要性权重 (1-4)
        """
        if not word:
            return 1

        importance = 1

        if len(word) >= 3:
            importance += 1
        elif len(word) >= 2:
            importance += 0.5

        complex_chars = 0
        for char in word:
            if ord(char) > 0x7000:
                complex_chars += 1

        if complex_chars >= len(word) * 0.5:
            importance += 1

        question_patterns = {'为什么', '什么', '怎么', '如何', '哪里', '哪个', '多少'}
        function_patterns = {'因为', '所以', '但是', '然后', '或者', '而且', '不过'}

        if word in question_patterns or word in function_patterns:
            importance = max(1, importance - 1)

        noun_endings = {'子', '者', '物', '体', '类', '种', '型', '式', '法', '术', '学'}
        if len(word) >= 2 and word[-1] in noun_endings:
            importance += 1

        if len(word) == 1:
            important_single_chars = {'天', '地', '人', '水', '火', '金', '木', '土', '光', '电'}
            if word in important_single_chars:
                importance += 1
            else:
                importance = 1

        return min(4, max(1, int(importance)))
    
    def load_data_chunks(self, data_path: str):
        """
        分块加载JSON数据
        """
        try:
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.statistics['original_count'] = len(data)
            self.logger.info(f"总数据量: {len(data)} 条")

            for i in range(0, len(data), self.chunk_size):
                chunk = data[i:i + self.chunk_size]
                self.logger.info(f"处理第 {i//self.chunk_size + 1} 块，包含 {len(chunk)} 条数据")
                yield chunk
                
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            raise
    
    def vectorize_texts(self, texts: List[str]) -> csr_matrix:
        """
        TF-IDF向量化文本
        """
        try:
            valid_texts = [text if text else "空文本" for text in texts]

            tfidf_matrix = self.vectorizer.fit_transform(valid_texts)

            self.logger.info(f"TF-IDF矩阵形状: {tfidf_matrix.shape}")
            self.logger.info(f"词汇表大小: {len(self.vectorizer.vocabulary_)}")

            return tfidf_matrix
            
        except Exception as e:
            self.logger.error(f"TF-IDF向量化失败: {e}")
            raise

    def calculate_similarity_matrix(self, tfidf_matrix: csr_matrix) -> csr_matrix:
        """
        分批计算余弦相似度矩阵，避免内存溢出
        """
        try:
            n_samples = tfidf_matrix.shape[0]
            self.logger.info(f"开始分批计算余弦相似度矩阵，数据量: {n_samples}")

            if n_samples <= 10000:
                return self._calculate_similarity_direct(tfidf_matrix)

            return self._calculate_similarity_batched(tfidf_matrix)

        except Exception as e:
            self.logger.error(f"计算相似度矩阵失败: {e}")
            raise

    def _calculate_similarity_direct(self, tfidf_matrix: csr_matrix) -> csr_matrix:
        """直接计算相似度矩阵（小数据量）"""
        self.logger.info("使用直接计算方法...")

        similarity_matrix = cosine_similarity(tfidf_matrix)

        similarity_matrix[similarity_matrix < self.similarity_threshold] = 0
        np.fill_diagonal(similarity_matrix, 0)

        high_sim_pairs = np.count_nonzero(similarity_matrix) // 2
        self.logger.info(f"发现 {high_sim_pairs} 对高相似度问题（阈值 >= {self.similarity_threshold}）")

        return csr_matrix(similarity_matrix)

    def _calculate_similarity_batched(self, tfidf_matrix: csr_matrix) -> csr_matrix:
        """分批计算相似度矩阵（大数据量）"""
        n_samples = tfidf_matrix.shape[0]
        batch_size = min(5000, n_samples // 10)

        self.logger.info(f"使用分批计算方法，批次大小: {batch_size}")

        similar_pairs = []

        for i in range(0, n_samples, batch_size):
            end_i = min(i + batch_size, n_samples)
            batch_i = tfidf_matrix[i:end_i]

            self.logger.info(f"处理批次 {i//batch_size + 1}/{(n_samples-1)//batch_size + 1}")

            for j in range(i, n_samples, batch_size):
                end_j = min(j + batch_size, n_samples)
                batch_j = tfidf_matrix[j:end_j]

                sim_batch = cosine_similarity(batch_i, batch_j)

                rows, cols = np.where(sim_batch >= self.similarity_threshold)

                for r, c in zip(rows, cols):
                    global_r = i + r
                    global_c = j + c

                    if global_r < global_c:
                        similar_pairs.append((global_r, global_c, sim_batch[r, c]))

        if similar_pairs:
            rows, cols, data = zip(*similar_pairs)
            all_rows = list(rows) + list(cols)
            all_cols = list(cols) + list(rows)
            all_data = list(data) + list(data)

            similarity_sparse = csr_matrix(
                (all_data, (all_rows, all_cols)),
                shape=(n_samples, n_samples)
            )
        else:
            similarity_sparse = csr_matrix((n_samples, n_samples))

        self.logger.info(f"发现 {len(similar_pairs)} 对高相似度问题（阈值 >= {self.similarity_threshold}）")

        return similarity_sparse

    def find_connected_components(self, similarity_matrix: csr_matrix) -> List[List[int]]:
        """
        使用连通分量算法识别需要合并的问题群组
        """
        try:
            self.logger.info("识别连通分量...")

            n_components, labels = connected_components(
                csgraph=similarity_matrix,
                directed=False,
                return_labels=True
            )

            groups = defaultdict(list)
            for idx, label in enumerate(labels):
                groups[label].append(idx)

            merge_groups = [group for group in groups.values() if len(group) > 1]

            self.logger.info(f"识别出 {len(merge_groups)} 个需要合并的群组")
            self.statistics['merged_groups'] = len(merge_groups)

            return merge_groups

        except Exception as e:
            self.logger.error(f"连通分量识别失败: {e}")
            raise

    def merge_similar_questions(self, data_chunk: List[Dict], merge_groups: List[List[int]]) -> List[Dict]:
        """
        合并相似问题，保留第一个问题的id和label
        """
        try:
            self.logger.info(f"开始合并 {len(merge_groups)} 个群组...")

            indices_to_remove = set()

            for group in merge_groups:
                if len(group) <= 1:
                    continue

                keep_idx = group[0]
                remove_indices = group[1:]

                keep_item = data_chunk[keep_idx]
                merged_items = [data_chunk[i] for i in remove_indices]

                merge_key = keep_item['id']
                self.merge_mapping[merge_key] = {
                    'kept_question': {
                        'id': keep_item['id'],
                        'content': keep_item['content'],
                        'label': keep_item['label']
                    },
                    'merged_questions': [
                        {
                            'id': item['id'],
                            'content': item['content'],
                            'label': item['label']
                        } for item in merged_items
                    ],
                    'merge_count': len(merged_items)
                }

                indices_to_remove.update(remove_indices)

            merged_data = [
                item for idx, item in enumerate(data_chunk)
                if idx not in indices_to_remove
            ]

            removed_count = len(indices_to_remove)
            self.logger.info(f"合并完成，删除了 {removed_count} 个重复问题")

            return merged_data

        except Exception as e:
            self.logger.error(f"合并问题失败: {e}")
            raise

    def process_chunk(self, chunk_data: List[Dict]) -> List[Dict]:
        """
        处理单个数据块的完整流程
        """
        try:
            self.logger.info(f"开始处理包含 {len(chunk_data)} 条数据的块")

            self.logger.info("步骤1: 文本预处理...")
            processed_texts = []
            for item in chunk_data:
                processed_text = self.preprocess_text(item.get('content', ''))
                processed_texts.append(processed_text)

            self.logger.info("步骤2: TF-IDF向量化...")
            tfidf_matrix = self.vectorize_texts(processed_texts)

            self.logger.info("步骤3: 计算相似度矩阵...")
            similarity_matrix = self.calculate_similarity_matrix(tfidf_matrix)

            self.logger.info("步骤4: 识别连通分量...")
            merge_groups = self.find_connected_components(similarity_matrix)

            self.logger.info("步骤5: 合并相似问题...")
            merged_data = self.merge_similar_questions(chunk_data, merge_groups)

            self.logger.info(f"块处理完成: {len(chunk_data)} -> {len(merged_data)} 条数据")

            return merged_data

        except Exception as e:
            self.logger.error(f"处理数据块失败: {e}")
            raise

    def create_output_directory(self, output_dir: str) -> None:
        """
        创建输出目录
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            self.logger.info(f"输出目录已创建: {output_dir}")
        except Exception as e:
            self.logger.error(f"创建输出目录失败: {e}")
            raise

    def generate_reports(self, merged_data: List[Dict], output_dir: str) -> None:
        """
        生成合并报告和输出文件
        """
        try:
            self.logger.info("生成输出文件和报告...")

            self.statistics['deduplicated_count'] = len(merged_data)

            output_file = os.path.join(output_dir, 'deduplicated_qa_dataset.json')
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(merged_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"去重数据集已保存: {output_file}")

            mapping_file = os.path.join(output_dir, 'merge_mapping.json')
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(self.merge_mapping, f, ensure_ascii=False, indent=2)
            self.logger.info(f"合并映射已保存: {mapping_file}")

            report_file = os.path.join(output_dir, 'deduplication_report.txt')
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=== 中文问答文本去重合并报告 ===\n\n")
                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"相似度阈值: {self.similarity_threshold}\n")
                f.write(f"分块大小: {self.chunk_size}\n\n")

                f.write("=== 统计信息 ===\n")
                f.write(f"原始问题数量: {self.statistics['original_count']:,}\n")
                f.write(f"去重后数量: {self.statistics['deduplicated_count']:,}\n")
                f.write(f"合并群组数量: {self.statistics['merged_groups']:,}\n")
                f.write(f"去重率: {(1 - self.statistics['deduplicated_count']/self.statistics['original_count'])*100:.2f}%\n")
                f.write(f"处理耗时: {self.statistics['processing_time']:.2f} 秒\n\n")

                f.write("=== 合并详情示例 ===\n")
                count = 0
                for merge_key, merge_info in self.merge_mapping.items():
                    if count >= 5:
                        break
                    f.write(f"\n群组 {count + 1}:\n")
                    f.write(f"  保留问题: {merge_info['kept_question']['content']}\n")
                    f.write(f"  合并问题:\n")
                    for merged_q in merge_info['merged_questions']:
                        f.write(f"    - {merged_q['content']}\n")
                    count += 1

                if len(self.merge_mapping) > 5:
                    f.write(f"\n... 还有 {len(self.merge_mapping) - 5} 个合并群组\n")

            self.logger.info(f"统计报告已保存: {report_file}")

        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            raise

    def run_deduplication(self, data_path: str, stopwords_path: str, output_dir: str) -> None:
        """
        运行完整的去重流程
        """
        start_time = time.time()

        try:
            self.logger.info("=== 开始中文问答文本去重合并 ===")

            self.load_stopwords(stopwords_path)

            self.create_output_directory(output_dir)

            all_merged_data = []
            chunk_count = 0

            for chunk_data in self.load_data_chunks(data_path):
                chunk_count += 1
                self.logger.info(f"\n=== 处理第 {chunk_count} 块数据 ===")

                merged_chunk = self.process_chunk(chunk_data)
                all_merged_data.extend(merged_chunk)

                del chunk_data, merged_chunk

            self.statistics['processing_time'] = time.time() - start_time
            self.generate_reports(all_merged_data, output_dir)

            self.logger.info("=== 去重合并完成 ===")
            self.logger.info(f"原始数据: {self.statistics['original_count']:,} 条")
            self.logger.info(f"去重后: {self.statistics['deduplicated_count']:,} 条")
            self.logger.info(f"去重率: {(1 - self.statistics['deduplicated_count']/self.statistics['original_count'])*100:.2f}%")
            self.logger.info(f"总耗时: {self.statistics['processing_time']:.2f} 秒")

        except Exception as e:
            self.logger.error(f"去重流程失败: {e}")
            raise





def main():
    """主函数"""
    data_path = r"/work1/data/fanzhang39/share/kexue/download/0710/seed_1.6_qa_out.json"
    stopwords_path = r"/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/hagongda_stopwords.txt"
    output_dir = r"/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/Deduplicate/output"

    deduplicator = TextDeduplicationTFIDF(
        similarity_threshold=0.96,
        chunk_size=50000
    )

    try:
        deduplicator.run_deduplication(data_path, stopwords_path, output_dir)

    except Exception as e:
        print(f"程序执行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
