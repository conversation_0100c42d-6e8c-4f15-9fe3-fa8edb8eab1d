import os
import sys
import time
from deduplication_tfidf import TextDeduplicationTFIDF


def test_threshold_batch(threshold: float, base_output_dir: str, input_dir: str, stopwords_path: str):
    """
    测试指定阈值的批量去重效果
    
    Args:
        threshold: 相似度阈值
        base_output_dir: 基础输出目录
        input_dir: 输入数据目录
        stopwords_path: 停用词文件路径
    """
    # 创建特定阈值的输出目录
    threshold_output_dir = os.path.join(base_output_dir, f"output_{threshold}")
    
    print(f"\n=== 开始测试阈值 {threshold} ===")
    print(f"输出目录: {threshold_output_dir}")
    
    # 创建去重器实例
    deduplicator = TextDeduplicationTFIDF(
        similarity_threshold=threshold,
        chunk_size=50000
    )
    
    try:
        # 运行批量去重
        deduplicator.run_batch_deduplication(input_dir, stopwords_path, threshold_output_dir)
        print(f"阈值 {threshold} 测试完成")
        return True
        
    except Exception as e:
        print(f"阈值 {threshold} 测试失败: {e}")
        return False


def main():
    """
    主函数：测试多个不同的相似度阈值
    """
    # 配置路径
    input_dir = r"/work1/data/fanzhang39/share/kexue/wjp/Type13/deduplication/data/classified"
    stopwords_path = r"/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/hagongda_stopwords.txt"
    base_output_dir = r"/work1/data/fanzhang39/share/kexue/wjp/Type13/deduplication/outputs/output"
    
    # 要测试的阈值列表
    thresholds = [0.60, 0.70]
    # , 0.88, 0.94, 0.96, 0.99]
    
    print("=== 多阈值去重测试开始 ===")
    print(f"输入目录: {input_dir}")
    print(f"停用词文件: {stopwords_path}")
    print(f"基础输出目录: {base_output_dir}")
    print(f"测试阈值: {thresholds}")
    
    start_time = time.time()
    results = {}
    
    # 逐个测试每个阈值
    for threshold in thresholds:
        threshold_start_time = time.time()
        
        success = test_threshold_batch(
            threshold=threshold,
            base_output_dir=base_output_dir,
            input_dir=input_dir,
            stopwords_path=stopwords_path
        )
        
        threshold_time = time.time() - threshold_start_time
        results[threshold] = {
            'success': success,
            'processing_time': threshold_time
        }
        
        print(f"阈值 {threshold} 处理时间: {threshold_time:.2f} 秒")
    
    total_time = time.time() - start_time
    
    # 输出总结
    print(f"\n=== 多阈值测试完成 ===")
    print(f"总处理时间: {total_time:.2f} 秒")
    print("\n各阈值测试结果:")
    
    for threshold, result in results.items():
        status = "成功" if result['success'] else "失败"
        print(f"  阈值 {threshold}: {status} (耗时: {result['processing_time']:.2f}秒)")
    
    # 生成对比报告
    generate_comparison_report(results, base_output_dir, total_time)
    
    return 0


def generate_comparison_report(results: dict, base_output_dir: str, total_time: float):
    """
    生成不同阈值的对比报告
    
    Args:
        results: 各阈值的测试结果
        base_output_dir: 基础输出目录
        total_time: 总处理时间
    """
    try:
        report_file = os.path.join(base_output_dir, "threshold_comparison_report.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=== 多阈值去重效果对比报告 ===\n\n")
            f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总处理时间: {total_time:.2f} 秒\n\n")
            
            f.write("=== 各阈值测试结果 ===\n")
            for threshold, result in results.items():
                status = "成功" if result['success'] else "失败"
                f.write(f"阈值 {threshold}: {status}\n")
                f.write(f"  处理时间: {result['processing_time']:.2f} 秒\n")
                f.write(f"  输出目录: output_{threshold}/\n\n")
            
            f.write("=== 使用说明 ===\n")
            f.write("1. 每个阈值的详细结果保存在对应的 output_X.XX 文件夹中\n")
            f.write("2. 查看各文件夹中的 batch_processing_summary.txt 了解具体统计信息\n")
            f.write("3. 阈值越低，合并的问题越多，去重率越高\n")
            f.write("4. 阈值越高，合并的问题越少，保留的原始问题越多\n")
        
        print(f"对比报告已保存: {report_file}")
        
    except Exception as e:
        print(f"生成对比报告失败: {e}")


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
