import json



# with open(input_file, "r", encoding="utf-8") as f:
#     all_data = json.load(f)
#     normal_data = []
#     for data in all_data:
#         if data["label"] == "正常":
#             normal_data.append(data)
# with open(output_file, "w", encoding="utf-8") as f:
#     json.dump(normal_data, f, ensure_ascii=False, indent=4)

def clean_json_file(input_file, output_file):

    with open(input_file, 'r', encoding='utf-8') as infile:
        all_data = json.load(infile)
    extracted_data = []
    if isinstance(all_data, list):
        for data in all_data:
            if 'output' in data and isinstance(data.get('output'), dict):
                extracted_data.append(data['output'])

    with open(output_file, 'w', encoding='utf-8') as outfile:
        for data in extracted_data:
            outfile.write(json.dumps(data, ensure_ascii=False) + '\n')

if __name__ == '__main__':
    input_file = 'sci_normal.json'
    output_file = 'sci_normal_clean.json'
    clean_json_file(input_file, output_file)
