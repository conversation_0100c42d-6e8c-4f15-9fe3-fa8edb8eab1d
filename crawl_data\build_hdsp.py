import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys


prompt = '''
# 任务描述
我需要你作为一名科学教师，为一道科学试题准备讲解脚本，严格遵循以下规范：

# 讲解脚本概要说明
- 需包含**读题→分析→答案→总结拓展**四环节，顺序不可调换，每个环节由一个或多个不同类型的脚本单元块构成。
- 脚本单元块有三种类型“讲题”、“知识传授”、“出选择题”。
- 如果答案包含多个空、多个选项、多个子问题、多个要点，讲解时应逐个讲解，不允许遗漏。
- 有些题目答案不唯一，按照提供的答案讲解即可。
- 尽量不要引用题目提供的材料以外的内容。

# 讲解脚本详细说明
## 脚本单元块输出格式说明
1. 脚本单元块以JSON格式输出包含以下字段
|字段名称|类型|释义|要求|
|---|---|---|
|step|str|脚本单元块环节|读题/分析/答案/总结拓展|
|type|str|脚本单元块类型|讲题/知识传授/出选择题|
|role|str|角色|固定为“老师”|
|cont|str|口播逐字稿|不允许出现无法直接读出的字符，如果逐字稿中涉及到不可读字符，参考下表替换成具体的提示词。|
|display_cont|type为讲题时，类型为str。type为知识传授或出选择题时，类型为dict|板书内容|①条理清晰，层次分明，重点突出；②分条列出逐字稿的核心意思；③要求必须使用书面化语言，需要去掉口语化描述，多使用符号化的描述。④使用<h></h>标记出关键词。⑤展示内容如果需要使用引号，必须优先使用中文双引号：“”。|
|mark_cont|list|题目标画内容|只在“分析-讲题”单元块标画口播逐字稿中提及的题干或材料的关键内容，不要标画题目的空或者题号，不要重复标画，使用两种特定标签进行标画：①句子-标画：与当前讲解内容相关的【试题】中的句子片段，使用<句子-标画></句子-标画>标记。②片段-高亮：读题环节，需要突出强调的【试题】中的片段。使用<片段-高亮></片段-高亮>标记，注意需要标记词语的上下文，用于准确定位。|

2. 口播逐字稿不可读字符替换说明
|不可读字符|替换内容|
|---|---|
|（）、______等作答区域符号|结合题干替换为“什么”“怎样的”“哪一个选项”“何种想法”“谁”等|
|“<point>”“<u>”“<wavy>”“<udouble>”“<i>”“<b>”等格式符号|表述成“加点”“加下划线”“加波浪线”“标双下划线”“斜体”“粗体”的字/词语/句子|

### 知识传授的板书（display_cont字段）说明
|字段名称|类型|释义|要求|
|---|---|---|---|
|name|str|知识传授的标题|要求高度概括。|
|content|str|知识传授的内容|是逐字稿的书面表述。可以使用<h></h>标记出关键词。|

### 出选择题的板书（display_cont字段）说明
|字段名称|类型|释义|要求|
|---|---|---|---|
|question|str|提问内容|①必须和讲题内容密切相关，不要设置“你理解了吗”“你懂了吗”这种太过简单、无意义的问题。②最好可以引出要能引出下一步对原题的讲解|
|options|dict|候选答案|①选项必须是2个，必须有且只有一个正确答案。②选择题的2个选项在本题范围以及尝试范围内都具有明显的区分度且无歧义（错误选项可以使用正确选项的反义或者明显不对的内容）。③格式为{"候选答案1":"正确/错误", "候选答案2":"正确/错误"}|

## 各类型脚本单元块要求
|环节|类型|语音逐字稿要求|板书要求|
|---|---|---|---|
|读题|讲题|完整、准确读出当前待讲解试题的题干。|为空|
|分析|讲题|紧跟“读题”的第一个“分析-讲题”，仅介绍考查知识点，不涉及题目分析，不用标画|固定格式“考查：<h>知识点</h>”，多个知识点用；拼接。|
|分析|讲题|后续的若干“分析-讲题”要求：①不要出现“根据参考信息”、“根据答案”、“但是答案是”这种话术；②题干提供信息不全时可以适当猜测题干内容，但不要出现“猜测性”的话术；③对于很基础的知识，只有在题目考点考查这些时进行讲解；|板书为逐字稿内容的高度总结凝练，结构化。|
|分析|知识传授|讲解涉及的概念知识、解题方法出示给学生|特定dict结构化呈现|
|分析|出选择题|语音逐字稿为空|特定dict结构化呈现|
|答案|讲题|通过简单引导语，汇总讲解，给出当前讲解试题的答案。必须与给定答案保持一致。|板书与答案一致|
|总结拓展|讲题| 回顾解题步骤与核心知识点。| 对应板书总结关键内容|

## 出选择题的脚本单元块说明
- “分析-出选择题”单元块数量控制在1到2个。
- 简单题型，不需要有“分析-出选择题”单元块。
- “分析-出选择题”单元块的上文的“分析-讲题”单元块不要出现“让我们来看一道选择题”、“接下来出道题”这样的话术。
- “分析-出选择题”单元块的下文的“分析-讲题”单元块不要讲解出的选择题。

# 输入输出样例
## 输入样例
下列说法中正确的是（）。\nA. 农民在种菜时，为了防止菜被虫蛀，应该尽可能多地喷洒农药\nB. 食物链通常以凶猛的肉食动物结束\nC. 在自然界中，有的生物小的不起眼，它们的存在对整个生态系统的平衡没有意义

【答案】
B

## 输出样例
<JSON>{"step": "读题", "type": "讲题", "role": "老师", "cont": "下列说法中正确的是哪一个选项。A选项说农民在种菜时，为了防止菜被虫蛀，应该尽可能多地喷洒农药。B选项说食物链通常以凶猛的肉食动物结束。C选项说在自然界中，有的生物小的不起眼，它们的存在对整个生态系统的平衡没有意义。", "display_cont": "", "mark_cont": []}</JSON>
<JSON>{"step": "分析", "type": "讲题", "role": "老师", "cont": "这道题考查的是生态系统和食物链的相关知识。", "display_cont": "考查：<h>生态系统；食物链</h>。", "mark_cont": []}</JSON>
<JSON>{"step": "分析", "type": "知识传授", "role": "老师", "cont": "首先，我们来了解一下食物链的知识。食物链是指在生态系统中，各种生物之间由于食物关系而形成的一种联系，通常从生产者开始，比如植物，然后依次是草食动物、肉食动物，一般以顶级肉食动物结束。另外，生态系统中的每一种生物都有其存在的意义，它们共同维持着生态系统的平衡。还有，农药虽然能防治害虫，但过多使用会带来很多危害，比如污染环境、危害人体健康等。", "display_cont": {"name": "生态系统与食物链相关知识", "content": "1. 食物链：<h>生态系统</h>中生物间因食物关系形成的联系，从<u>生产者</u>（植物）开始，依次为<u>草食动物</u>、<u>肉食动物</u>，通常以<u>顶级肉食动物</u>结束。<br>2. 生态系统平衡：每种生物都有存在意义，共同维持平衡。<br>3. 农药使用：过多喷洒会<u>污染环境</u>、<u>危害人体健康</u>等。"}, "mark_cont": []}</JSON>
<JSON>{"step": "分析", "type": "讲题", "role": "老师", "cont": "接下来我们分析各个选项。先看A选项，农民种菜时为防止菜被虫蛀，尽可能多地喷洒农药。刚才我们提到过多使用农药会污染环境和危害人体健康，所以不能尽可能多地喷洒农药，A选项错误。", "display_cont": "A选项：过多喷洒农药会<u>污染环境</u>、<u>危害人体健康</u>，错误。", "mark_cont": []}</JSON>
<JSON>{"step": "分析", "type": "出选择题", "role": "老师", "cont": "", "display_cont": {"question": "下列关于农药使用的说法，正确的是？", "options": {"应合理使用农药，减少对环境的污染": "正确", "为防治害虫，可大量使用农药": "错误"}}, "mark_cont": []}</JSON>
<JSON>{"step": "分析", "type": "讲题", "role": "老师", "cont": "再看B选项，食物链通常以凶猛的肉食动物结束，这和我们刚才讲的食物链通常以顶级肉食动物结束是一致的，所以B选项正确。", "display_cont": "B选项：食物链通常以<u>顶级肉食动物</u>结束，正确。", "mark_cont": []}</JSON>
<JSON>{"step": "分析", "type": "讲题", "role": "老师", "cont": "最后看C选项，说自然界中有的生物小的不起眼，它们的存在对生态系统平衡没有意义。但我们知道生态系统中的每一种生物都有其存在的意义，共同维持着生态系统的平衡，所以C选项错误。", "display_cont": "C选项：每种生物都<u>维持生态系统平衡</u>，错误。", "mark_cont": []}</JSON>
<JSON>{"step": "答案", "type": "讲题", "role": "老师", "cont": "综合以上分析，正确的选项是B。", "display_cont": "B", "mark_cont": []}</JSON>
<JSON>{"step": "总结拓展", "type": "讲题", "role": "老师", "cont": "这道题主要考查了我们对生态系统和食物链知识的掌握。我们要记住食物链的构成特点，以及生态系统中每种生物的重要性，还有合理使用农药的知识。以后遇到类似题目，要根据这些知识点来分析选项。", "display_cont": "1. 食物链：从生产者开始，以顶级肉食动物结束。<br>2. 每种生物都维持生态系统平衡。<br>3. 合理使用农药。", "mark_cont": []}</JSON>
# 当前输入
【试题】
{{content}}

【答案】
{{answer}}

严格遵守上述要求，每个单元块用<JSON></JSON>标记，单元块内容为JSON。'''


def convert(line):
    try:        
        data = json.loads(line)
        
        raw_question = data['content']
        raw_answer = data['answer']

        d = {}
        d['id'] = data['id']
        d['query'] = prompt.replace('{{content}}', raw_question).replace('{{answer}}', raw_answer).strip()

    except Exception as e:
        print(e)
        # print(data)
        return None

    return d


data_path = '/work1/data/fanzhang39/projects/kexue/dati/label/0630/kexue_no_blank_20250630.txt'
save_file = '/work1/data/fanzhang39/projects/kexue/dati/label/0630/kexue_20250630_hdsp_1w.txt'
print(data_path)

total = 0
res = []
f = open(data_path, encoding='utf-8')
with Pool(64) as p:
    all_data = p.imap(convert, f)
    # print(len(all_data))
    for data in all_data:
        total += 1
        if data:
            res.append(data)

# print(ans) 
# count = Counter(ans)
# print(len(res))
# print(total)
# print(count)

with open(save_file, 'w', encoding='utf-8') as f:
    for item in res:
        f.write(json.dumps(item, ensure_ascii=False) + '\n')
print(save_file)