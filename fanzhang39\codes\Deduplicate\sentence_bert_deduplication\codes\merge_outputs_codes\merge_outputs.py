#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并去重后的JSON文件脚本
将多个标签的JSON文件合并成一个，按照原始ID排序
"""

import json
import os
import glob
from typing import List, Dict, Any


def load_json_files(input_dir: str) -> List[Dict[str, Any]]:
    """
    加载输入目录中的所有JSON文件（排除mapping文件）
    
    Args:
        input_dir: 输入目录路径
        
    Returns:
        合并后的数据列表
    """
    all_data = []
    
    # 获取所有JSON文件，排除mapping文件
    json_files = glob.glob(os.path.join(input_dir, "*.json"))
    json_files = [f for f in json_files if not f.endswith("_mapping.json")]
    
    print(f"找到 {len(json_files)} 个JSON文件需要合并:")
    
    for json_file in json_files:
        filename = os.path.basename(json_file)
        print(f"  - 正在处理: {filename}")
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if isinstance(data, list):
                all_data.extend(data)
                print(f"    加载了 {len(data)} 条记录")
            else:
                print(f"    警告: {filename} 不是列表格式，跳过")
                
        except Exception as e:
            print(f"    错误: 无法加载 {filename}: {e}")
    
    print(f"\n总共加载了 {len(all_data)} 条记录")
    return all_data


def sort_by_id(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    按照ID字段排序数据
    
    Args:
        data: 原始数据列表
        
    Returns:
        排序后的数据列表
    """
    print("正在按ID排序...")
    
    # 转换ID为整数进行排序
    def get_sort_key(item):
        try:
            return int(item.get('id', 0))
        except (ValueError, TypeError):
            return 0
    
    sorted_data = sorted(data, key=get_sort_key)
    print(f"排序完成，ID范围: {sorted_data[0]['id']} - {sorted_data[-1]['id']}")
    
    return sorted_data


def save_merged_data(data: List[Dict[str, Any]], output_path: str) -> None:
    """
    保存合并后的数据
    
    Args:
        data: 要保存的数据
        output_path: 输出文件路径
    """
    print(f"正在保存到: {output_path}")
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"成功保存 {len(data)} 条记录到 {output_path}")
        
    except Exception as e:
        print(f"保存失败: {e}")
        raise


def main():
    """主函数"""
    # 输入和输出路径配置
    input_dir = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/outputs/outputs_7_16/output_0.95"
    output_dir = "/work1/data/fanzhang39/share/kexue/wjp/Type13/sentence_bert_deduplication/datas/merged_datas"
    output_filename = "merged_datas_round1_0.95.json"
    output_path = os.path.join(output_dir, output_filename)
    
    print("=" * 60)
    print("JSON文件合并脚本")
    print("=" * 60)
    print(f"输入目录: {input_dir}")
    print(f"输出路径: {output_path}")
    print("=" * 60)
    
    # 检查输入目录是否存在
    if not os.path.exists(input_dir):
        print(f"错误: 输入目录不存在: {input_dir}")
        return
    
    try:
        # 1. 加载所有JSON文件
        all_data = load_json_files(input_dir)
        
        if not all_data:
            print("没有找到有效的数据，退出")
            return
        
        # 2. 按ID排序
        sorted_data = sort_by_id(all_data)
        
        # 3. 保存合并结果
        save_merged_data(sorted_data, output_path)
        
        # 4. 输出统计信息
        print("\n" + "=" * 60)
        print("合并完成！统计信息:")
        print(f"  - 总记录数: {len(sorted_data)}")
        
        # 统计各标签的数量
        label_counts = {}
        for item in sorted_data:
            label = item.get('label', 'Unknown')
            label_counts[label] = label_counts.get(label, 0) + 1
        
        print("  - 各标签记录数:")
        for label, count in sorted(label_counts.items()):
            print(f"    {label}: {count}")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
