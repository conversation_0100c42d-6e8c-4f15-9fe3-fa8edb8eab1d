#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生物类问答数据K-means聚类分析
对biology_qa_data.json中的生物类问题进行主题聚类分析
"""


import json
import re
import warnings
import time
from collections import Counter
from datetime import datetime, timedelta


import jieba
import jieba.posseg as pseg
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from sklearn.cluster import DBSCAN, KMeans
from sklearn.decomposition import PCA
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import calinski_harabasz_score, silhouette_score
from sklearn.metrics.pairwise import pairwise_distances
from sklearn.preprocessing import StandardScaler

# 在所有导入完成后执行配置
warnings.filterwarnings('ignore')


class SemanticMatchingConfig:
    """语义匹配算法配置类"""

    def __init__(self):
        # 相似度阈值配置
        self.similarity_threshold = 0.95  # 语义相似度阈值
        self.weight_threshold = 0.8       # 权重相似度阈值
        self.structure_threshold = 0.7    # 结构相似度阈值

        # TF-IDF配置
        self.max_features = 2000          # 最大特征数
        self.min_df = 1                   # 最小文档频率
        self.max_df = 0.95               # 最大文档频率
        self.use_binary = True           # 使用二进制权重

        # 聚类配置
        self.prefer_more_clusters = True  # 倾向于更多聚类
        self.semantic_purity_weight = 0.8 # 语义纯度权重
        self.silhouette_weight = 0.2     # 轮廓系数权重

        # 词汇权重配置
        self.base_weight = 0.5           # 基础权重
        self.length_bonus = 0.2          # 长度奖励
        self.complexity_bonus = 0.1      # 复杂度奖励

    def update_config(self, **kwargs):
        """更新配置参数"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                print(f"警告：未知配置参数 {key}")

    def get_strict_config(self):
        """获取超严格模式配置"""
        config = SemanticMatchingConfig()
        config.similarity_threshold = 0.98
        config.weight_threshold = 0.9
        config.structure_threshold = 0.8
        return config

    def get_moderate_config(self):
        """获取中等严格模式配置"""
        config = SemanticMatchingConfig()
        config.similarity_threshold = 0.85
        config.weight_threshold = 0.7
        config.structure_threshold = 0.6
        return config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class BiologyQAClusterAnalyzer:
    """通用语义匹配聚类分析器"""

    def __init__(self, data_path, config=None):
        """
        初始化分析器

        Args:
            data_path: 数据文件路径
            config: 语义匹配配置对象，如果为None则使用默认配置
        """
        self.data_path = data_path
        self.data = None
        self.processed_texts = None
        self.tfidf_matrix = None
        self.vectorizer = None
        self.optimal_k = None
        self.kmeans_model = None
        self.cluster_labels = None

        # 配置系统
        self.config = config if config is not None else SemanticMatchingConfig()

        # 时间跟踪
        self.start_time = None
        self.step_times = {}

        # 加载哈工大停用词表
        self.stop_words = self._load_stopwords()

    def _log_time(self, step_name, start_time=None):
        """
        记录和显示时间信息

        Args:
            step_name: 步骤名称
            start_time: 步骤开始时间，如果为None则记录当前时间作为开始
        """
        current_time = time.time()
        current_datetime = datetime.now()

        if start_time is None:
            # 记录步骤开始时间
            self.step_times[step_name] = current_time
            print(f"[{current_datetime.strftime('%H:%M:%S')}] 开始: {step_name}")
            return current_time
        else:
            # 计算并显示步骤耗时
            elapsed = current_time - start_time
            elapsed_str = str(timedelta(seconds=int(elapsed)))
            print(f"[{current_datetime.strftime('%H:%M:%S')}] 完成: {step_name} (耗时: {elapsed_str})")

            # 显示总体进度
            if self.start_time:
                total_elapsed = current_time - self.start_time
                total_elapsed_str = str(timedelta(seconds=int(total_elapsed)))
                print(f"    总耗时: {total_elapsed_str}")

            return elapsed

    def _estimate_remaining_time(self, current_step, total_steps, step_start_time):
        """
        估算剩余时间

        Args:
            current_step: 当前步骤数
            total_steps: 总步骤数
            step_start_time: 当前步骤开始时间
        """
        if current_step > 0:
            elapsed = time.time() - step_start_time
            avg_time_per_step = elapsed
            remaining_steps = total_steps - current_step
            estimated_remaining = avg_time_per_step * remaining_steps

            if estimated_remaining > 0:
                remaining_str = str(timedelta(seconds=int(estimated_remaining)))
                progress = (current_step / total_steps) * 100
                print(f"    进度: {current_step}/{total_steps} ({progress:.1f}%) | 预计剩余: {remaining_str}")

    def _print_separator(self, title=""):
        """打印分隔线"""
        if title:
            print(f"\n{'='*20} {title} {'='*20}")
        else:
            print("="*60)

    def _load_stopwords(self):
        """
        加载哈工大停用词表

        Returns:
            set: 停用词集合
        """
        stopwords_file = "hagongda_stopwords.txt"
        stop_words = set()

        try:
            with open(stopwords_file, 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip()
                    if word:  # 过滤空行
                        stop_words.add(word)
            print(f"成功加载 {len(stop_words)} 个停用词")
        except FileNotFoundError:
            print(f"警告: 停用词文件 {stopwords_file} 未找到，使用默认停用词")
            # 如果文件不存在，使用原来的默认停用词
            stop_words = {
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
                '自己', '这', '那', '什么', '为什么', '怎么', '如何', '吗', '呢', '啊', '呀'
            }
        except Exception as e:
            print(f"加载停用词文件时出错: {e}，使用默认停用词")
            stop_words = {
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
                '自己', '这', '那', '什么', '为什么', '怎么', '如何', '吗', '呢', '啊', '呀'
            }

        return stop_words

    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 条生物类问答数据")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def preprocess_text(self, text):
        """
        简化的文本预处理方法
        Args:
            text: 原始文本
        Returns:
            处理后的文本
        """
        if not text or not isinstance(text, str):
            return ""

        # 使用jieba进行基础分词和词性标注
        words = []
        for word, flag in pseg.cut(text):
            # 基本过滤条件
            if (len(word) > 1 and
                word not in self.stop_words and
                flag in ['n', 'v', 'a', 'nr', 'ns', 'nt', 'nz', 'vn', 'an']):
                # 过滤纯数字和标点
                if not re.match(r'^[\d\W]+$', word):
                    words.append(word)

        return ' '.join(words)

    def _extract_semantic_signature(self, text):
        """
        基于通用语言学原理提取语义签名

        Args:
            text: 预处理后的文本

        Returns:
            tuple: (内容词集合, 词汇权重字典) 的语义签名
        """
        if not text or not isinstance(text, str):
            return (set(), {})

        words = text.split()

        # 基于通用语言学的功能词过滤（不依赖特定领域）
        functional_words = {
            # 疑问词类
            '什么', '怎么', '如何', '哪里', '哪个', '多少', '几个', '什么样',
            '怎样', '为何', '为什么', '怎么样', '何时', '何地', '哪些', '哪种',

            # 连接词类
            '因为', '所以', '但是', '然后', '还有', '或者', '而且', '不过',
            '可是', '只是', '就是', '也是', '都是', '还是', '要是', '如果',

            # 程度副词类
            '可能', '应该', '一般', '通常', '经常', '总是', '有时', '偶尔',
            '特别', '非常', '很', '比较', '相当', '十分', '极其', '最',
            '那么', '这么', '如此', '这样', '那样', '多么', '太', '挺',

            # 代词和指示词类
            '人们', '大家', '我们', '你们', '他们', '这个', '那个',
            '这些', '那些', '这里', '那里', '这样', '那样',

            # 助动词和系动词类
            '会', '能', '要', '想', '可以', '应该', '必须', '需要', '得到',
            '进行', '发生', '出现', '产生', '形成', '变成', '成为', '具有',
            '拥有', '存在', '属于', '包含', '含有', '带有', '是', '有',

            # 时空概念词类
            '时候', '地方', '时间', '空间', '地点', '位置', '方向', '地区',

            # 抽象概念词类
            '东西', '事情', '问题', '情况', '现象', '方面', '方式', '方法',
            '原因', '结果', '作用', '影响', '关系', '联系', '区别', '差异'
        }

        # 提取内容词（实词）
        content_words = set()
        word_weights = {}

        for word in words:
            if (len(word) > 1 and
                word not in functional_words and
                not word.isdigit() and
                word not in ['的', '了', '着', '过', '呢', '吗', '吧']):

                content_words.add(word)

                # 基于词汇特征计算权重（不依赖特定词汇）
                weight = self._calculate_word_semantic_weight(word)
                word_weights[word] = weight

        return (content_words, word_weights)

    def _calculate_word_semantic_weight(self, word):
        """
        基于通用语言学特征计算词汇的语义权重

        Args:
            word: 词汇

        Returns:
            float: 语义权重 (0-1)
        """
        weight = self.config.base_weight  # 使用配置的基础权重

        # 长度特征：较长的词通常语义更具体
        if len(word) >= 3:
            weight += self.config.length_bonus
        elif len(word) == 2:
            weight += self.config.length_bonus * 0.5

        # 复合词特征：包含常见语义成分的词权重更高
        # 使用通用的语义成分，不限定特定领域
        semantic_components = ['色', '光', '纹', '壳', '空', '声', '味', '形', '状', '性',
                             '质', '量', '度', '力', '能', '感', '觉', '象', '态', '式']
        if any(comp in word for comp in semantic_components):
            weight += self.config.length_bonus

        # 词汇复杂度：包含多个语义单元的词权重更高
        if len(set(word)) >= 2:  # 字符多样性
            weight += self.config.complexity_bonus

        # 词汇稀有度：在当前文档集合中出现频率较低的词权重更高
        # 这里可以根据需要添加基于文档频率的权重调整

        return min(weight, 1.0)  # 确保权重不超过1

    def _extract_semantic_core(self, text):
        """保持向后兼容，返回所有语义核心词汇"""
        entities, phenomena, modifiers = self._extract_semantic_signature(text)
        return entities.union(phenomena).union(modifiers)

    def _extract_core_words(self, text):
        """保持向后兼容的方法名"""
        return self._extract_semantic_core(text)

    def _compute_semantic_similarity(self, text1, text2):
        """
        基于通用语义匹配原理计算文本相似度

        Args:
            text1: 第一个文本
            text2: 第二个文本

        Returns:
            float: 语义相似度分数（0.0 或 1.0）
        """
        # 1. 提取语义签名
        content_words1, weights1 = self._extract_semantic_signature(text1)
        content_words2, weights2 = self._extract_semantic_signature(text2)

        # 2. 检查是否有足够的语义内容
        if len(content_words1) == 0 or len(content_words2) == 0:
            return 0.0  # 没有内容词，不应该聚类

        # 3. 计算加权语义相似度

        # 3.1 词汇集合重叠度
        intersection = content_words1.intersection(content_words2)
        union = content_words1.union(content_words2)

        if len(intersection) == 0:
            return 0.0  # 没有共同词汇

        # 3.2 基础重叠比例
        overlap_ratio = len(intersection) / len(union)

        # 3.3 加权相似度计算
        # 计算共同词汇的权重贡献
        common_weight_sum = sum(max(weights1.get(word, 0), weights2.get(word, 0)) for word in intersection)
        total_weight_sum = sum(weights1.values()) + sum(weights2.values()) - common_weight_sum

        if total_weight_sum == 0:
            weighted_similarity = overlap_ratio
        else:
            weighted_similarity = common_weight_sum / total_weight_sum

        # 3.4 语义结构一致性检查
        # 检查两个文本的语义结构是否相似
        structure_similarity = self._calculate_structure_similarity(content_words1, content_words2, weights1, weights2)

        # 3.5 综合相似度评估
        # 结合词汇重叠、权重相似度和结构相似度
        final_similarity = (overlap_ratio * 0.4 + weighted_similarity * 0.4 + structure_similarity * 0.2)

        # 3.6 超严格阈值判断（使用配置参数）
        if (final_similarity >= self.config.similarity_threshold and
            weighted_similarity >= self.config.weight_threshold and
            structure_similarity >= self.config.structure_threshold):
            return 1.0  # 完美匹配
        else:
            return 0.0  # 不匹配

    def _calculate_structure_similarity(self, words1, words2, weights1, weights2):
        """
        计算两个词汇集合的结构相似度

        Args:
            words1, words2: 词汇集合
            weights1, weights2: 权重字典

        Returns:
            float: 结构相似度 (0-1)
        """
        # 计算高权重词汇的比例相似度
        high_weight_words1 = {w for w, weight in weights1.items() if weight >= 0.7}
        high_weight_words2 = {w for w, weight in weights2.items() if weight >= 0.7}

        if len(high_weight_words1) == 0 and len(high_weight_words2) == 0:
            return 1.0  # 都没有高权重词，结构相似

        if len(high_weight_words1) == 0 or len(high_weight_words2) == 0:
            return 0.0  # 一个有高权重词，一个没有，结构不同

        # 高权重词汇的重叠度
        high_weight_intersection = high_weight_words1.intersection(high_weight_words2)
        high_weight_union = high_weight_words1.union(high_weight_words2)

        if len(high_weight_union) == 0:
            return 1.0

        return len(high_weight_intersection) / len(high_weight_union)

    def _compute_precise_similarity(self, text1, text2):
        """保持向后兼容的方法名"""
        return self._compute_semantic_similarity(text1, text2)

    def _validate_semantic_consistency(self, questions_group):
        """
        验证一组问题的语义一致性

        Args:
            questions_group: 问题列表

        Returns:
            tuple: (is_consistent, consistency_score, core_concepts)
        """
        if len(questions_group) <= 1:
            return True, 1.0, set()

        # 提取所有问题的语义核心
        all_cores = []
        for question in questions_group:
            processed = self.preprocess_text(question)
            core = self._extract_semantic_core(processed)
            all_cores.append(core)

        # 计算共同的语义核心
        common_core = set.intersection(*all_cores) if all_cores else set()
        all_concepts = set.union(*all_cores) if all_cores else set()

        # 计算语义一致性分数
        if len(all_concepts) == 0:
            consistency_score = 0.0
        else:
            consistency_score = len(common_core) / len(all_concepts)

        # 判断是否语义一致（要求至少80%的概念是共同的）
        is_consistent = consistency_score >= 0.8 and len(common_core) > 0

        return is_consistent, consistency_score, common_core

    def _calculate_semantic_purity(self, cluster_labels):
        """
        计算聚类的语义纯度分数

        Args:
            cluster_labels: 聚类标签数组

        Returns:
            float: 语义纯度分数 (0-1)
        """
        if len(cluster_labels) == 0:
            return 0.0

        similarity_matrix = 1 - self.precise_distance_matrix
        total_purity = 0.0
        valid_clusters = 0

        for cluster_id in set(cluster_labels):
            cluster_indices = np.where(cluster_labels == cluster_id)[0]
            cluster_size = len(cluster_indices)

            if cluster_size < 2:
                # 单个问题的聚类，纯度为1
                total_purity += 1.0
                valid_clusters += 1
                continue

            # 计算聚类内部的语义一致性
            internal_similarities = []
            for i in range(len(cluster_indices)):
                for j in range(i + 1, len(cluster_indices)):
                    idx1, idx2 = cluster_indices[i], cluster_indices[j]
                    sim = similarity_matrix[idx1, idx2]
                    internal_similarities.append(sim)

            if internal_similarities:
                # 语义纯度 = 完美匹配对的比例
                perfect_matches = sum(1 for sim in internal_similarities if sim >= 1.0)
                cluster_purity = perfect_matches / len(internal_similarities)
                total_purity += cluster_purity
                valid_clusters += 1

        return total_purity / valid_clusters if valid_clusters > 0 else 0.0

    def _create_semantic_distance_matrix(self):
        """
        创建基于语义核心匹配的距离矩阵

        Returns:
            numpy.ndarray: 距离矩阵
        """
        n = len(self.processed_texts)
        print(f"    计算语义核心距离矩阵 ({n}x{n})...")

        # 预先提取所有文本的语义核心（提高效率）
        semantic_cores = []
        for text in self.processed_texts:
            core = self._extract_semantic_core(text)
            semantic_cores.append(core)

        print(f"    语义核心提取完成，开始计算相似度...")

        # 创建相似度矩阵
        similarity_matrix = np.zeros((n, n), dtype=np.float32)
        semantic_matches = 0

        for i in range(n):
            similarity_matrix[i, i] = 1.0  # 对角线为1

            for j in range(i + 1, n):
                # 使用预提取的语义核心计算相似度
                core1, core2 = semantic_cores[i], semantic_cores[j]

                # 快速语义匹配检查
                if len(core1) == 0 or len(core2) == 0:
                    sim = 0.0
                else:
                    intersection = core1.intersection(core2)
                    union = core1.union(core2)

                    if len(intersection) == 0:
                        sim = 0.0
                    else:
                        overlap_ratio = len(intersection) / len(union)
                        size_ratio = min(len(core1), len(core2)) / max(len(core1), len(core2))

                        # 超严格匹配条件
                        if overlap_ratio >= 0.9 and size_ratio >= 0.7:
                            sim = 1.0
                            semantic_matches += 1
                        else:
                            sim = 0.0

                similarity_matrix[i, j] = sim
                similarity_matrix[j, i] = sim

            # 显示进度
            if (i + 1) % 1000 == 0:
                progress = ((i + 1) / n) * 100
                print(f"      进度: {i + 1}/{n} ({progress:.1f}%) | 语义匹配对数: {semantic_matches}")

        print(f"    发现 {semantic_matches} 个语义匹配问题对")

        # 显示语义核心统计
        non_empty_cores = sum(1 for core in semantic_cores if len(core) > 0)
        print(f"    有效语义核心数量: {non_empty_cores}/{n}")

        # 计算距离矩阵
        distance_matrix = 1 - similarity_matrix

        return distance_matrix

    def _create_precise_distance_matrix(self):
        """保持向后兼容的方法名"""
        return self._create_semantic_distance_matrix()








    
    def prepare_features(self):
        """准备TF-IDF特征数据"""
        # 文本预处理
        preprocess_start = time.time()
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 开始文本预处理...")

        # 提取问题文本
        texts = [item['content'] for item in self.data]
        print(f"    原始数据量: {len(texts):,} 条")

        # 预处理文本（显示进度）
        processed_count = 0
        self.processed_texts = []
        for i, text in enumerate(texts):
            processed_text = self.preprocess_text(text)
            self.processed_texts.append(processed_text)
            processed_count += 1

            # 每处理10000条显示一次进度
            if processed_count % 10000 == 0:
                progress = (processed_count / len(texts)) * 100
                elapsed = time.time() - preprocess_start
                rate = processed_count / elapsed
                remaining = (len(texts) - processed_count) / rate
                print(f"    预处理进度: {processed_count:,}/{len(texts):,} ({progress:.1f}%) | "
                      f"速度: {rate:.0f}条/秒 | 预计剩余: {remaining:.0f}秒")

        preprocess_time = time.time() - preprocess_start
        print(f"    文本预处理完成，耗时: {preprocess_time:.1f}秒")

        # 过滤空文本
        filter_start = time.time()
        valid_indices = [i for i, text in enumerate(self.processed_texts) if text.strip()]
        self.processed_texts = [self.processed_texts[i] for i in valid_indices]
        self.data = [self.data[i] for i in valid_indices]
        filter_time = time.time() - filter_start

        print(f"    过滤空文本完成，有效文本数量: {len(self.processed_texts):,} 条 (耗时: {filter_time:.1f}秒)")

        # TF-IDF向量化（使用配置参数）
        tfidf_start = time.time()
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 开始TF-IDF向量化（通用语义匹配模式）...")
        self.vectorizer = TfidfVectorizer(
            max_features=self.config.max_features,    # 使用配置的最大特征数
            min_df=self.config.min_df,                # 使用配置的最小文档频率
            max_df=self.config.max_df,                # 使用配置的最大文档频率
            ngram_range=(1, 1),                       # 只使用单词，避免n-gram模糊化
            binary=self.config.use_binary,            # 使用配置的二进制权重设置
            norm='l2',                                # L2归一化
            use_idf=True,                             # 使用IDF权重
            smooth_idf=False,                         # 不平滑IDF，保持词汇区分度
            sublinear_tf=False                        # 不使用对数TF，保持原始权重
        )

        self.tfidf_matrix = self.vectorizer.fit_transform(self.processed_texts)
        tfidf_time = time.time() - tfidf_start

        print(f"    TF-IDF向量化完成，耗时: {tfidf_time:.1f}秒")
        print(f"    TF-IDF矩阵形状: {self.tfidf_matrix.shape}")
        print(f"    矩阵稀疏度: {(1 - self.tfidf_matrix.nnz / (self.tfidf_matrix.shape[0] * self.tfidf_matrix.shape[1])) * 100:.2f}%")

        # 计算语义核心距离矩阵
        semantic_start = time.time()
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 计算语义核心距离矩阵...")
        self.precise_distance_matrix = self._create_semantic_distance_matrix()
        semantic_time = time.time() - semantic_start
        print(f"    语义核心距离矩阵计算完成，耗时: {semantic_time:.1f}秒")
    
    def find_optimal_k(self, max_k=None):
        """
        寻找最优聚类数量（超严格语义匹配模式）

        Args:
            max_k: 最大聚类数，如果为None则自动确定
        """
        self._print_separator("寻找最优聚类数量（超严格语义匹配模式）")
        step_start = self._log_time("寻找最优聚类数量")

        # 分析数据特征，确定合理的k值范围
        print("分析超严格匹配模式下的数据特征...")

        data_size = len(self.processed_texts)
        similarity_matrix = 1 - self.precise_distance_matrix

        # 统计完美匹配对的数量（相似度 = 1.0）
        perfect_pairs = np.sum(similarity_matrix >= 1.0) - data_size  # 减去对角线
        perfect_pairs = perfect_pairs // 2  # 除以2因为矩阵对称

        # 统计有任何相似度的对数量
        any_sim_pairs = np.sum(similarity_matrix > 0.0) - data_size
        any_sim_pairs = any_sim_pairs // 2

        print(f"    数据总量: {data_size}")
        print(f"    完美匹配对数: {perfect_pairs}")
        print(f"    有相似度对数: {any_sim_pairs}")

        # 自适应确定k值范围
        if max_k is None:
            # 基于完美匹配对数量估算最大聚类数
            # 理想情况：每个完美匹配对形成一个聚类，其余问题各自独立
            estimated_clusters = data_size - perfect_pairs
            max_k = min(data_size, max(estimated_clusters, data_size // 2))
            print(f"    自动估算最大聚类数: {max_k}")

        # 确保k值范围合理
        min_k = 2
        max_k = min(max_k, data_size)  # 不能超过数据量

        # 对于超严格匹配，我们倾向于更多的聚类
        if max_k < data_size // 3:
            max_k = min(data_size, data_size // 2)
            print(f"    调整为更多聚类数，新的最大值: {max_k}")

        k_range = range(min_k, max_k + 1)
        total_k_values = len(k_range)
        inertias = []
        silhouette_scores = []
        calinski_scores = []
        precise_scores = []  # 基于精确距离矩阵的轮廓系数
        semantic_purity_scores = []  # 语义纯度分数

        print(f"将测试 k={min(k_range)} 到 k={max(k_range)} 共 {total_k_values} 个聚类数量")
        print("注意：超严格模式倾向于选择更多的聚类数以确保语义纯度")

        for i, k in enumerate(k_range, 1):
            k_start = time.time()
            print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 测试 k={k} ({i}/{total_k_values})")

            # 使用TF-IDF进行K-means聚类
            kmeans_start = time.time()
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(self.tfidf_matrix)
            kmeans_time = time.time() - kmeans_start
            print(f"    K-means聚类完成 (耗时: {kmeans_time:.1f}秒)")

            # 计算评估指标
            metrics_start = time.time()
            inertia = kmeans.inertia_

            # 基于TF-IDF的评估指标
            silhouette_tfidf = silhouette_score(self.tfidf_matrix, cluster_labels)
            calinski_tfidf = calinski_harabasz_score(self.tfidf_matrix.toarray(), cluster_labels)

            # 基于精确距离矩阵的评估指标
            try:
                silhouette_precise = silhouette_score(self.precise_distance_matrix, cluster_labels, metric='precomputed')
            except:
                silhouette_precise = 0.0

            # 计算语义纯度分数（最重要的指标）
            semantic_purity = self._calculate_semantic_purity(cluster_labels)

            metrics_time = time.time() - metrics_start

            inertias.append(inertia)
            silhouette_scores.append(silhouette_tfidf)
            calinski_scores.append(calinski_tfidf)
            precise_scores.append(silhouette_precise)
            semantic_purity_scores.append(semantic_purity)

            k_total_time = time.time() - k_start
            print(f"    评估指标计算完成 (耗时: {metrics_time:.1f}秒)")
            print(f"    k={k} 总耗时: {k_total_time:.1f}秒")
            print(f"    TF-IDF轮廓系数: {silhouette_tfidf:.4f}")
            print(f"    精确匹配轮廓系数: {silhouette_precise:.4f}")
            print(f"    🎯 语义纯度: {semantic_purity:.4f} (最重要指标)")

            # 估算剩余时间
            if i > 1:  # 从第二个k值开始估算
                avg_time = (time.time() - step_start) / i
                remaining_k = total_k_values - i
                estimated_remaining = avg_time * remaining_k
                remaining_str = str(timedelta(seconds=int(estimated_remaining)))
                progress = (i / total_k_values) * 100
                print(f"    进度: {i}/{total_k_values} ({progress:.1f}%) | 预计剩余: {remaining_str}")

        # 绘制评估指标（包含精确匹配评估）
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 肘部法则
        axes[0, 0].plot(k_range, inertias, 'bo-')
        axes[0, 0].set_xlabel('聚类数量 (k)')
        axes[0, 0].set_ylabel('簇内平方和 (Inertia)')
        axes[0, 0].set_title('肘部法则')
        axes[0, 0].grid(True)

        # TF-IDF轮廓系数
        axes[0, 1].plot(k_range, silhouette_scores, 'ro-', label='TF-IDF')
        axes[0, 1].plot(k_range, precise_scores, 'go-', label='精确匹配')
        axes[0, 1].set_xlabel('聚类数量 (k)')
        axes[0, 1].set_ylabel('轮廓系数')
        axes[0, 1].set_title('轮廓系数对比')
        axes[0, 1].legend()
        axes[0, 1].grid(True)

        # Calinski-Harabasz指数
        axes[1, 0].plot(k_range, calinski_scores, 'mo-')
        axes[1, 0].set_xlabel('聚类数量 (k)')
        axes[1, 0].set_ylabel('Calinski-Harabasz指数')
        axes[1, 0].set_title('Calinski-Harabasz指数')
        axes[1, 0].grid(True)

        # 综合评分（精确匹配权重更高）
        combined_scores = []
        for i in range(len(k_range)):
            # 综合评分：70%精确匹配 + 30%TF-IDF
            combined = 0.7 * precise_scores[i] + 0.3 * silhouette_scores[i]
            combined_scores.append(combined)

        axes[1, 1].plot(k_range, combined_scores, 'co-')
        axes[1, 1].set_xlabel('聚类数量 (k)')
        axes[1, 1].set_ylabel('综合评分')
        axes[1, 1].set_title('综合评分（精确匹配优先）')
        axes[1, 1].grid(True)

        plt.tight_layout()

        # 确保输出目录存在
        import os
        output_dir = '/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/out_kmeans'
        os.makedirs(output_dir, exist_ok=True)

        plt.savefig(f'{output_dir}/optimal_k_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 选择最优k（优先考虑语义纯度）
        print(f"\n=== 最优聚类数量选择（语义纯度优先）===")

        # 策略1：选择语义纯度最高的k值
        best_purity_idx = np.argmax(semantic_purity_scores)
        best_purity_k = k_range[best_purity_idx]
        best_purity_score = semantic_purity_scores[best_purity_idx]

        # 策略2：在高语义纯度中选择聚类数更多的（更严格）
        high_purity_threshold = 0.8  # 语义纯度阈值
        high_purity_indices = [i for i, score in enumerate(semantic_purity_scores) if score >= high_purity_threshold]

        if high_purity_indices:
            # 在高纯度的k值中选择最大的（更多聚类）
            best_strict_idx = max(high_purity_indices, key=lambda i: k_range[i])
            best_strict_k = k_range[best_strict_idx]
            print(f"发现 {len(high_purity_indices)} 个高语义纯度的k值 (≥{high_purity_threshold})")
            print(f"选择最严格的k值: {best_strict_k} (语义纯度: {semantic_purity_scores[best_strict_idx]:.4f})")
            self.optimal_k = best_strict_k
            best_idx = best_strict_idx
        else:
            # 没有高纯度的k值，选择纯度最高的
            print(f"没有达到高纯度阈值的k值，选择纯度最高的: {best_purity_k}")
            self.optimal_k = best_purity_k
            best_idx = best_purity_idx

        # 综合评分（使用配置权重）
        combined_scores = []
        for i in range(len(k_range)):
            # 使用配置的权重进行综合评分
            combined = (self.config.semantic_purity_weight * semantic_purity_scores[i] +
                       self.config.silhouette_weight * precise_scores[i])
            combined_scores.append(combined)

        print(f"\n推荐的最优聚类数量: {self.optimal_k}")
        print(f"该k值的评估指标:")
        print(f"  - 🎯 语义纯度: {semantic_purity_scores[best_idx]:.4f} (主要指标)")
        print(f"  - 精确匹配轮廓系数: {precise_scores[best_idx]:.4f}")
        print(f"  - TF-IDF轮廓系数: {silhouette_scores[best_idx]:.4f}")
        print(f"  - 综合评分: {combined_scores[best_idx]:.4f}")

        # 打印详细评估结果
        results_df = pd.DataFrame({
            'k': k_range,
            'semantic_purity': semantic_purity_scores,
            'precise_silhouette': precise_scores,
            'tfidf_silhouette': silhouette_scores,
            'calinski_score': calinski_scores,
            'combined_score': combined_scores,
            'inertia': inertias
        })
        print("\n聚类评估结果（按语义纯度排序）:")
        print(results_df.sort_values('semantic_purity', ascending=False).round(4))
    
    def perform_clustering(self, k=None):
        """
        执行精确匹配K-means聚类

        Args:
            k: 聚类数量，如果为None则使用最优k
        """
        if k is None:
            k = self.optimal_k

        self._print_separator(f"执行精确匹配K-means聚类 (k={k})")
        cluster_start = self._log_time("K-means聚类")

        self.kmeans_model = KMeans(n_clusters=k, random_state=42, n_init=10)
        self.cluster_labels = self.kmeans_model.fit_predict(self.tfidf_matrix)

        # 计算多种评估指标
        print("计算聚类质量评估指标...")

        # 基于TF-IDF的评估指标
        silhouette_tfidf = silhouette_score(self.tfidf_matrix, self.cluster_labels)
        calinski_tfidf = calinski_harabasz_score(self.tfidf_matrix.toarray(), self.cluster_labels)

        # 基于精确距离矩阵的评估指标
        try:
            silhouette_precise = silhouette_score(self.precise_distance_matrix, self.cluster_labels, metric='precomputed')
        except:
            silhouette_precise = 0.0

        self._log_time("K-means聚类", cluster_start)

        print(f"\n=== 聚类质量评估 ===")
        print(f"TF-IDF轮廓系数: {silhouette_tfidf:.4f}")
        print(f"精确匹配轮廓系数: {silhouette_precise:.4f}")
        print(f"Calinski-Harabasz指数: {calinski_tfidf:.4f}")

        # 统计每个聚类的大小和质量
        cluster_counts = Counter(self.cluster_labels)
        print(f"\n=== 聚类分布 ===")
        print(f"聚类数量: {len(cluster_counts)}")

        for cluster_id in sorted(cluster_counts.keys()):
            count = cluster_counts[cluster_id]
            percentage = (count / len(self.cluster_labels)) * 100
            print(f"聚类 {cluster_id}: {count:,} 个问题 ({percentage:.1f}%)")

        # 验证聚类质量：检查每个聚类内部的精确相似度
        self._validate_cluster_quality()

    def _validate_cluster_quality(self):
        """
        验证聚类质量：检查每个聚类内部的语义一致性
        """
        print(f"\n=== 语义一致性验证 ===")

        similarity_matrix = 1 - self.precise_distance_matrix
        cluster_quality_stats = []
        semantic_violations = []

        for cluster_id in sorted(set(self.cluster_labels)):
            # 获取该聚类的所有问题
            cluster_indices = np.where(self.cluster_labels == cluster_id)[0]
            cluster_questions = [self.data[i]['content'] for i in cluster_indices]
            cluster_size = len(cluster_questions)

            print(f"\n聚类 {cluster_id} ({cluster_size}个问题):")
            for i, question in enumerate(cluster_questions):
                processed = self.processed_texts[cluster_indices[i]]
                core = self._extract_semantic_core(processed)
                print(f"  {i+1}. {question}")
                print(f"     语义核心: {core}")

            if cluster_size < 2:
                print(f"     ✅ 单个问题，语义一致")
                continue

            # 验证语义一致性
            is_consistent, consistency_score, common_core = self._validate_semantic_consistency(cluster_questions)

            # 计算数值相似度统计
            internal_similarities = []
            perfect_matches = 0

            for i in range(len(cluster_indices)):
                for j in range(i + 1, len(cluster_indices)):
                    idx1, idx2 = cluster_indices[i], cluster_indices[j]
                    sim = similarity_matrix[idx1, idx2]
                    internal_similarities.append(sim)
                    if sim >= 1.0:  # 完美匹配
                        perfect_matches += 1

            if internal_similarities:
                avg_similarity = np.mean(internal_similarities)
                min_similarity = np.min(internal_similarities)
                perfect_ratio = perfect_matches / len(internal_similarities)

                cluster_quality_stats.append({
                    'cluster_id': cluster_id,
                    'size': cluster_size,
                    'is_consistent': is_consistent,
                    'consistency_score': consistency_score,
                    'avg_similarity': avg_similarity,
                    'min_similarity': min_similarity,
                    'perfect_ratio': perfect_ratio,
                    'common_core': common_core
                })

                # 判断聚类质量
                if is_consistent and perfect_ratio >= 0.8:
                    quality_status = "🎯 优秀"
                elif is_consistent and perfect_ratio >= 0.5:
                    quality_status = "👍 良好"
                elif consistency_score >= 0.6:
                    quality_status = "⚠️ 一般"
                else:
                    quality_status = "❌ 错误"
                    semantic_violations.append({
                        'cluster_id': cluster_id,
                        'questions': cluster_questions,
                        'consistency_score': consistency_score
                    })

                print(f"     语义一致性: {consistency_score:.3f}")
                print(f"     共同核心: {common_core}")
                print(f"     完美匹配比例: {perfect_ratio:.1%}")
                print(f"     质量评估: {quality_status}")

        # 总体质量统计
        if cluster_quality_stats:
            consistent_clusters = sum(1 for stat in cluster_quality_stats if stat['is_consistent'])
            total_clusters = len(cluster_quality_stats)
            overall_consistency = np.mean([stat['consistency_score'] for stat in cluster_quality_stats])
            overall_perfect_ratio = np.mean([stat['perfect_ratio'] for stat in cluster_quality_stats])

            print(f"\n=== 总体语义质量评估 ===")
            print(f"语义一致的聚类: {consistent_clusters}/{total_clusters} ({consistent_clusters/total_clusters:.1%})")
            print(f"平均语义一致性: {overall_consistency:.3f}")
            print(f"平均完美匹配比例: {overall_perfect_ratio:.1%}")

            if len(semantic_violations) == 0:
                print("🎉 优秀！所有聚类都具有语义一致性")
            elif len(semantic_violations) <= total_clusters * 0.2:
                print("👍 良好！大部分聚类具有语义一致性")
            else:
                print("❌ 需要改进！存在较多语义不一致的聚类")
                print("\n语义违规聚类:")
                for violation in semantic_violations:
                    print(f"  聚类 {violation['cluster_id']} (一致性: {violation['consistency_score']:.3f}):")
                    for q in violation['questions']:
                        print(f"    - {q}")

        return len(semantic_violations) == 0
    
    def visualize_clusters(self):
        """可视化聚类结果"""
        print("生成聚类可视化...")

        # PCA降维到2D
        pca = PCA(n_components=2, random_state=42)
        tfidf_2d = pca.fit_transform(self.tfidf_matrix.toarray())

        # 创建可视化
        plt.figure(figsize=(12, 8))
        scatter = plt.scatter(tfidf_2d[:, 0], tfidf_2d[:, 1],
                            c=self.cluster_labels, cmap='tab10', alpha=0.7)
        plt.colorbar(scatter)
        plt.xlabel(f'PC1 (解释方差: {pca.explained_variance_ratio_[0]:.2%})')
        plt.ylabel(f'PC2 (解释方差: {pca.explained_variance_ratio_[1]:.2%})')
        plt.title('生物类问答聚类结果 (PCA降维)')

        # 添加聚类中心
        centers_2d = pca.transform(self.kmeans_model.cluster_centers_)
        plt.scatter(centers_2d[:, 0], centers_2d[:, 1],
                   c='red', marker='x', s=200, linewidths=3, label='聚类中心')
        plt.legend()

        plt.grid(True, alpha=0.3)

        # 确保输出目录存在
        import os
        output_dir = '/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/out_kmeans'
        os.makedirs(output_dir, exist_ok=True)

        plt.savefig(f'{output_dir}/cluster_visualization.png', dpi=300, bbox_inches='tight')
        plt.show()

        print(f"PCA解释的总方差: {sum(pca.explained_variance_ratio_):.2%}")

    def analyze_clusters(self):
        """分析每个聚类的特征"""
        print("分析聚类特征...")

        # 获取特征词汇（兼容不同版本的scikit-learn）
        try:
            # 新版本scikit-learn
            feature_names = self.vectorizer.get_feature_names_out()
        except AttributeError:
            # 旧版本scikit-learn
            feature_names = self.vectorizer.get_feature_names()

        cluster_analysis = {}

        for cluster_id in range(self.optimal_k):
            # 获取该聚类的问题
            cluster_indices = np.where(self.cluster_labels == cluster_id)[0]
            cluster_questions = [self.data[i]['content'] for i in cluster_indices]

            # 计算该聚类的TF-IDF中心
            cluster_center = self.kmeans_model.cluster_centers_[cluster_id]

            # 获取最重要的特征词
            top_features_idx = cluster_center.argsort()[-10:][::-1]
            top_features = [feature_names[i] for i in top_features_idx]
            top_scores = [cluster_center[i] for i in top_features_idx]

            # 选择代表性问题（距离聚类中心最近的问题）
            cluster_tfidf = self.tfidf_matrix[cluster_indices]
            distances = np.linalg.norm(cluster_tfidf.toarray() - cluster_center, axis=1)
            representative_indices = distances.argsort()[:5]
            representative_questions = [cluster_questions[i] for i in representative_indices]

            cluster_analysis[cluster_id] = {
                'size': len(cluster_questions),
                'top_features': list(zip(top_features, top_scores)),
                'representative_questions': representative_questions,
                'all_questions': cluster_questions
            }

            print(f"\n=== 聚类 {cluster_id} ===")
            print(f"问题数量: {len(cluster_questions)}")
            print(f"关键特征词: {', '.join(top_features[:5])}")
            print("代表性问题:")
            for i, q in enumerate(representative_questions, 1):
                print(f"  {i}. {q}")

        return cluster_analysis

    def save_results(self, cluster_analysis):
        """保存聚类结果"""
        print("保存聚类结果...")

        # 确保输出目录存在
        import os
        output_dir = '/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/out_kmeans'
        os.makedirs(output_dir, exist_ok=True)

        # 为原数据添加聚类标签
        results = []
        for i, item in enumerate(self.data):
            result_item = item.copy()
            result_item['cluster_id'] = int(self.cluster_labels[i])
            result_item['processed_text'] = self.processed_texts[i]
            results.append(result_item)

        # 保存带聚类标签的数据
        with open(f'{output_dir}/biology_qa_clustered.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 保存聚类分析结果
        analysis_results = {
            'optimal_k': self.optimal_k,
            'total_questions': len(self.data),
            'cluster_analysis': cluster_analysis,
            'evaluation_metrics': {
                'silhouette_score': float(silhouette_score(self.tfidf_matrix, self.cluster_labels)),
                'calinski_harabasz_score': float(calinski_harabasz_score(self.tfidf_matrix.toarray(), self.cluster_labels))
            }
        }

        with open(f'{output_dir}/cluster_analysis_results.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, ensure_ascii=False, indent=2)

        print(f"结果已保存到: {output_dir}")
        print("- biology_qa_clustered.json (带聚类标签的原数据)")
        print("- cluster_analysis_results.json (聚类分析结果)")

    def generate_report(self, cluster_analysis):
        """生成聚类分析报告"""
        print("\n" + "="*60)
        print("生物类问答聚类分析报告")
        print("="*60)

        print(f"数据概况:")
        print(f"- 总问题数量: {len(self.data)}")
        print(f"- 最优聚类数: {self.optimal_k}")
        print(f"- 特征维度: {self.tfidf_matrix.shape[1]}")

        print(f"\n聚类质量评估:")
        silhouette_avg = silhouette_score(self.tfidf_matrix, self.cluster_labels)
        calinski_score = calinski_harabasz_score(self.tfidf_matrix.toarray(), self.cluster_labels)
        print(f"- 轮廓系数: {silhouette_avg:.4f}")
        print(f"- Calinski-Harabasz指数: {calinski_score:.4f}")

        print(f"\n主题聚类发现:")
        for cluster_id, analysis in cluster_analysis.items():
            print(f"\n聚类 {cluster_id}:")
            top_words = [word for word, _ in analysis['top_features'][:3]]

            print(f"  问题数量: {analysis['size']}")
            print(f"  关键词: {', '.join(top_words)}")

    def run_complete_analysis(self):
        """运行完整的聚类分析流程"""
        self._print_separator("生物类问答K-means聚类分析")
        self.start_time = self._log_time("完整聚类分析流程")

        try:
            # 1. 加载数据
            step1_start = self._log_time("加载数据")
            if not self.load_data():
                return
            self._log_time("加载数据", step1_start)

            # 2. 准备特征
            step2_start = self._log_time("文本预处理和特征提取")
            self.prepare_features()
            self._log_time("文本预处理和特征提取", step2_start)

            # 3. 寻找最优聚类数
            step3_start = self._log_time("寻找最优聚类数")
            self.find_optimal_k()
            self._log_time("寻找最优聚类数", step3_start)

            # 4. 执行聚类
            step4_start = self._log_time("执行K-means聚类")
            self.perform_clustering()
            self._log_time("执行K-means聚类", step4_start)

            # 5. 可视化结果
            step5_start = self._log_time("生成可视化图表")
            self.visualize_clusters()
            self._log_time("生成可视化图表", step5_start)

            # 6. 分析聚类特征
            step6_start = self._log_time("分析聚类特征")
            cluster_analysis = self.analyze_clusters()
            self._log_time("分析聚类特征", step6_start)

            # 7. 保存结果
            step7_start = self._log_time("保存结果文件")
            self.save_results(cluster_analysis)
            self._log_time("保存结果文件", step7_start)

            # 8. 生成报告
            step8_start = self._log_time("生成分析报告")
            self.generate_report(cluster_analysis)
            self._log_time("生成分析报告", step8_start)

            # 总结
            total_time = self._log_time("完整聚类分析流程", self.start_time)
            self._print_separator("分析完成")
            print(f"🎉 聚类分析成功完成！")
            print(f"📊 数据量: {len(self.data):,} 条")
            print(f"⏱️  总耗时: {str(timedelta(seconds=int(total_time)))}")
            print(f"🎯 最优聚类数: {self.optimal_k}")

        except Exception as e:
            print(f"\n❌ 分析过程中出现错误: {e}")
            if self.start_time:
                elapsed = time.time() - self.start_time
                print(f"⏱️  已运行时间: {str(timedelta(seconds=int(elapsed)))}")
            raise


def main():
    """主函数"""
    # 数据文件路径
    data_path = "/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/data_biology_only/biology_qa_data.json"

    # 创建分析器并运行分析
    analyzer = BiologyQAClusterAnalyzer(data_path)
    analyzer.run_complete_analysis()


if __name__ == "__main__":
    main()
