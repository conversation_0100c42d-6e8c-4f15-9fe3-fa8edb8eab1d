"""
BGE-M3文本去重系统运行脚本
简化版本，使用默认配置
"""

# 解决TLS冲突问题 - 必须在导入其他库之前设置
import os
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from bge_m3_deduplication import BGE_M3_Deduplicator
from config import Config
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, Config.LOG_LEVEL),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(Config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def main():
    """主函数 - 单GPU模式运行"""

    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("=== BGE-M3文本去重系统启动（单GPU模式）===")

    try:
        # 验证配置
        Config.validate_paths()
        logger.info("配置验证通过")
        
        # 显示GPU状态信息
        gpu_info = Config.get_gpu_memory_info()
        if gpu_info:
            logger.info("=== GPU状态信息 ===")
            for device, info in gpu_info.items():
                logger.info(f"{device}: {info['usage_percent']:.1f}% 已使用, {info['free']:.1f}GB 可用")

        # 创建去重器
        device = Config.get_device()
        logger.info(f"选择设备: {device}")

        deduplicator = BGE_M3_Deduplicator(
            model_path=Config.MODEL_PATH,
            device=device
        )

        # 设置配置参数
        deduplicator.batch_size = Config.BATCH_SIZE  # BGE-M3编码批处理
        deduplicator.max_length = Config.MAX_LENGTH
        
        # 创建输出目录
        os.makedirs(Config.OUTPUT_PATH, exist_ok=True)
        
        # 查找输入文件
        input_files = [f for f in os.listdir(Config.INPUT_PATH) if f.endswith('.json')]
        
        if not input_files:
            logger.warning("未找到JSON文件")
            return
        
        logger.info(f"找到 {len(input_files)} 个JSON文件: {input_files}")
        
        # 处理每个文件
        for file_name in input_files:
            logger.info(f"开始处理: {file_name}")
            input_file = os.path.join(Config.INPUT_PATH, file_name)
            
            try:
                deduplicator.process_file(
                    input_file=input_file,
                    output_dir=Config.OUTPUT_PATH,
                    thresholds=Config.SIMILARITY_THRESHOLDS
                )
                logger.info(f"文件 {file_name} 处理完成")
                
            except Exception as e:
                logger.error(f"处理文件 {file_name} 时出错: {e}")
                continue
        
        logger.info("=== 所有文件处理完成 ===")
        
        # 输出结果摘要
        logger.info(f"结果保存在: {Config.OUTPUT_PATH}")
        for threshold in Config.SIMILARITY_THRESHOLDS:
            threshold_dir = os.path.join(Config.OUTPUT_PATH, f"output_{threshold}")
            if os.path.exists(threshold_dir):
                files_count = len([f for f in os.listdir(threshold_dir) if f.endswith('.json')])
                logger.info(f"  阈值 {threshold}: {files_count} 个去重文件")
        
    except Exception as e:
        logger.error(f"系统运行出错: {e}")
        raise

if __name__ == "__main__":
    main()
