#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生物类问答数据K-means聚类分析
对biology_qa_data.json中的生物类问题进行主题聚类分析
"""


import json
import re
import warnings
from collections import Counter


import jieba
import jieba.posseg as pseg
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from sklearn.cluster import DBSCAN, KMeans
from sklearn.decomposition import PCA
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import calinski_harabasz_score, silhouette_score
from sklearn.metrics.pairwise import pairwise_distances
from sklearn.preprocessing import StandardScaler

# 在所有导入完成后执行配置
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class BiologyQAClusterAnalyzer:
    """生物类问答聚类分析器"""
    
    def __init__(self, data_path):
        """
        初始化分析器

        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.data = None
        self.processed_texts = None
        self.word_sets = None  # 存储每个问题的词汇集合
        self.similarity_matrix = None  # 存储相似度矩阵
        self.distance_matrix = None  # 存储距离矩阵
        self.optimal_eps = None  # DBSCAN的最优eps参数
        self.clustering_model = None  # 聚类模型
        self.cluster_labels = None

        # 聚类参数设置
        self.jaccard_threshold = 0.8  # Jaccard相似度阈值
        self.min_samples = 1  # DBSCAN最小样本数（设为1避免噪声点）

        # 加载哈工大停用词表
        self.stop_words = self._load_stopwords()

    def _load_stopwords(self):
        """
        加载哈工大停用词表

        Returns:
            set: 停用词集合
        """
        stopwords_file = "hagongda_stopwords.txt"
        stop_words = set()

        try:
            with open(stopwords_file, 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip()
                    if word:  # 过滤空行
                        stop_words.add(word)
            print(f"成功加载 {len(stop_words)} 个停用词")
        except FileNotFoundError:
            print(f"警告: 停用词文件 {stopwords_file} 未找到，使用默认停用词")
            # 如果文件不存在，使用原来的默认停用词
            stop_words = {
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
                '自己', '这', '那', '什么', '为什么', '怎么', '如何', '吗', '呢', '啊', '呀'
            }
        except Exception as e:
            print(f"加载停用词文件时出错: {e}，使用默认停用词")
            stop_words = {
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
                '自己', '这', '那', '什么', '为什么', '怎么', '如何', '吗', '呢', '啊', '呀'
            }

        return stop_words

    def load_data(self):
        """加载数据"""
        try:
            with open(self.data_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 条生物类问答数据")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def preprocess_text(self, text):
        """
        简化的文本预处理方法
        Args:
            text: 原始文本
        Returns:
            处理后的文本
        """
        if not text or not isinstance(text, str):
            return ""

        # 使用jieba进行基础分词和词性标注
        words = []
        for word, flag in pseg.cut(text):
            # 基本过滤条件
            if (len(word) > 1 and
                word not in self.stop_words and
                flag in ['n', 'v', 'a', 'nr', 'ns', 'nt', 'nz', 'vn', 'an']):
                # 过滤纯数字和标点
                if not re.match(r'^[\d\W]+$', word):
                    words.append(word)

        return ' '.join(words)

    def extract_word_sets(self):
        """
        将预处理后的文本转换为词汇集合

        Returns:
            list: 每个问题对应的词汇集合列表
        """
        word_sets = []
        for text in self.processed_texts:
            if text.strip():
                # 将文本分割为词汇集合
                words = set(text.split())
                word_sets.append(words)
            else:
                word_sets.append(set())

        return word_sets

    def jaccard_similarity(self, set1, set2):
        """
        计算两个词汇集合的Jaccard相似度

        Args:
            set1: 第一个词汇集合
            set2: 第二个词汇集合

        Returns:
            float: Jaccard相似度 (0-1之间)
        """
        if len(set1) == 0 and len(set2) == 0:
            return 1.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        if union == 0:
            return 0.0

        return intersection / union



    def compute_similarity_matrix(self):
        """
        计算所有问题对之间的相似度矩阵

        Returns:
            numpy.ndarray: 相似度矩阵
        """
        n = len(self.word_sets)
        similarity_matrix = np.zeros((n, n))

        print(f"计算 {n}x{n} 相似度矩阵...")

        for i in range(n):
            for j in range(i, n):
                if i == j:
                    similarity_matrix[i][j] = 1.0
                else:
                    # 使用Jaccard相似度
                    sim = self.jaccard_similarity(self.word_sets[i], self.word_sets[j])
                    similarity_matrix[i][j] = sim
                    similarity_matrix[j][i] = sim

            # 显示进度
            if (i + 1) % 1000 == 0:
                print(f"已完成 {i + 1}/{n} 行")

        return similarity_matrix
    
    def prepare_features(self):
        """准备基于词汇集合的特征数据"""
        print("开始文本预处理...")

        # 提取问题文本
        texts = [item['content'] for item in self.data]

        # 预处理文本
        self.processed_texts = [self.preprocess_text(text) for text in texts]

        # 过滤空文本
        valid_indices = [i for i, text in enumerate(self.processed_texts) if text.strip()]
        self.processed_texts = [self.processed_texts[i] for i in valid_indices]
        self.data = [self.data[i] for i in valid_indices]

        print(f"预处理完成，有效文本数量: {len(self.processed_texts)}")

        # 提取词汇集合
        print("提取词汇集合...")
        self.word_sets = self.extract_word_sets()

        # 计算相似度矩阵
        print("计算词汇相似度矩阵...")
        self.similarity_matrix = self.compute_similarity_matrix()

        # 转换为距离矩阵（距离 = 1 - 相似度）
        self.distance_matrix = 1 - self.similarity_matrix

        print(f"相似度矩阵形状: {self.similarity_matrix.shape}")

        # 统计高相似度对的数量
        high_sim_count = np.sum(self.similarity_matrix >= self.jaccard_threshold) - len(self.data)  # 减去对角线
        print(f"相似度 >= {self.jaccard_threshold} 的问题对数量: {high_sim_count // 2}")  # 除以2因为矩阵对称
    
    def find_optimal_eps(self, eps_range=None):
        """
        寻找DBSCAN的最优eps参数

        Args:
            eps_range: eps参数范围，默认基于相似度阈值自动生成
        """
        print("寻找DBSCAN最优eps参数...")

        if eps_range is None:
            # 基于Jaccard相似度阈值生成eps范围
            # eps = 1 - jaccard_similarity，所以高相似度对应低eps
            max_eps = 1 - 0.6  # 最低相似度0.6
            min_eps = 1 - 0.95  # 最高相似度0.95
            eps_range = np.linspace(min_eps, max_eps, 20)

        results = []

        for eps in eps_range:
            # 使用DBSCAN进行聚类
            dbscan = DBSCAN(eps=eps, min_samples=self.min_samples, metric='precomputed')
            cluster_labels = dbscan.fit_predict(self.distance_matrix)

            # 计算聚类统计信息
            n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
            n_noise = list(cluster_labels).count(-1)
            n_clustered = len(cluster_labels) - n_noise

            # 计算评估指标（排除噪声点）
            if n_clusters > 1 and n_clustered > n_clusters:
                # 只对非噪声点计算轮廓系数
                non_noise_indices = cluster_labels != -1
                if np.sum(non_noise_indices) > 1:
                    try:
                        silhouette_avg = silhouette_score(
                            self.distance_matrix[non_noise_indices][:, non_noise_indices],
                            cluster_labels[non_noise_indices],
                            metric='precomputed'
                        )
                    except:
                        silhouette_avg = -1
                else:
                    silhouette_avg = -1
            else:
                silhouette_avg = -1

            results.append({
                'eps': eps,
                'n_clusters': n_clusters,
                'n_noise': n_noise,
                'n_clustered': n_clustered,
                'silhouette_score': silhouette_avg,
                'cluster_ratio': n_clustered / len(cluster_labels) if len(cluster_labels) > 0 else 0
            })

        # 转换为DataFrame
        results_df = pd.DataFrame(results)

        # 选择最优eps：平衡聚类质量和聚类比例
        valid_results = results_df[
            (results_df['n_clusters'] >= 2) &
            (results_df['silhouette_score'] >= 0) &
            (results_df['cluster_ratio'] >= 0.8)  # 至少80%的点被聚类（减少噪声）
        ]

        if len(valid_results) > 0:
            # 选择轮廓系数最高的eps
            best_idx = valid_results['silhouette_score'].idxmax()
            self.optimal_eps = valid_results.loc[best_idx, 'eps']
        else:
            # 如果没有合适的结果，使用更宽松的eps值减少噪声
            self.optimal_eps = 1 - 0.6  # 相当于0.6的相似度阈值

        print(f"推荐的最优eps: {self.optimal_eps:.4f}")
        print(f"对应的Jaccard相似度阈值: {1 - self.optimal_eps:.4f}")

        print("\nDBSCAN参数评估结果:")
        print(results_df.round(4))
    
    def perform_clustering(self, eps=None):
        """
        执行基于词汇相似度的DBSCAN聚类

        Args:
            eps: DBSCAN的eps参数，如果为None则使用最优eps
        """
        if eps is None:
            eps = self.optimal_eps

        print(f"使用DBSCAN进行聚类，eps={eps:.4f}, min_samples={self.min_samples}...")

        # 创建DBSCAN聚类模型
        self.clustering_model = DBSCAN(
            eps=eps,
            min_samples=self.min_samples,
            metric='precomputed'
        )

        # 对距离矩阵进行聚类
        self.cluster_labels = self.clustering_model.fit_predict(self.distance_matrix)

        # 计算聚类统计信息
        n_clusters = len(set(self.cluster_labels)) - (1 if -1 in self.cluster_labels else 0)
        n_noise = list(self.cluster_labels).count(-1)
        n_clustered = len(self.cluster_labels) - n_noise

        print(f"聚类完成!")
        print(f"聚类数量: {n_clusters}")
        print(f"噪声点数量: {n_noise}")
        print(f"被聚类的点数量: {n_clustered}")
        print(f"聚类比例: {n_clustered / len(self.cluster_labels):.2%}")

        # 计算评估指标（排除噪声点）
        if n_clusters > 1 and n_clustered > n_clusters:
            non_noise_indices = self.cluster_labels != -1
            if np.sum(non_noise_indices) > 1:
                try:
                    silhouette_avg = silhouette_score(
                        self.distance_matrix[non_noise_indices][:, non_noise_indices],
                        self.cluster_labels[non_noise_indices],
                        metric='precomputed'
                    )
                    print(f"轮廓系数（非噪声点）: {silhouette_avg:.4f}")
                except:
                    print("无法计算轮廓系数")

        # 统计每个聚类的大小
        cluster_counts = Counter(self.cluster_labels)
        print("\n各聚类大小:")
        for cluster_id in sorted(cluster_counts.keys()):
            if cluster_id == -1:
                print(f"噪声点: {cluster_counts[cluster_id]} 个问题")
            else:
                print(f"聚类 {cluster_id}: {cluster_counts[cluster_id]} 个问题")
    
    def visualize_clusters(self):
        """可视化基于词汇相似度的聚类结果"""
        print("生成聚类可视化...")

        # 使用MDS（多维标度）降维，更适合距离矩阵
        from sklearn.manifold import MDS

        # 使用MDS将距离矩阵降维到2D
        mds = MDS(n_components=2, dissimilarity='precomputed', random_state=42)
        coords_2d = mds.fit_transform(self.distance_matrix)

        # 创建可视化
        plt.figure(figsize=(12, 8))

        # 为噪声点和聚类点使用不同的颜色
        unique_labels = set(self.cluster_labels)
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_labels)))

        for k, col in zip(unique_labels, colors):
            if k == -1:
                # 噪声点用黑色
                col = 'black'
                marker = 'x'
                alpha = 0.5
                label = '噪声点'
            else:
                marker = 'o'
                alpha = 0.7
                label = f'聚类 {k}'

            class_member_mask = (self.cluster_labels == k)
            xy = coords_2d[class_member_mask]
            plt.scatter(xy[:, 0], xy[:, 1], c=[col], marker=marker,
                       alpha=alpha, s=50, label=label)

        plt.xlabel('MDS维度1')
        plt.ylabel('MDS维度2')
        plt.title('生物类问答词汇相似度聚类结果 (MDS降维)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('/fanzhang39/codes/qa/k-means/cluster_visualization.png', dpi=300, bbox_inches='tight')
        plt.show()

        print(f"MDS应力值: {mds.stress_:.4f}")  # 应力值越小，降维效果越好

    def analyze_clusters(self):
        """分析每个聚类的特征"""
        print("分析聚类特征...")

        # 获取特征词汇
        feature_names = self.vectorizer.get_feature_names_out()

        cluster_analysis = {}

        for cluster_id in range(self.optimal_k):
            # 获取该聚类的问题
            cluster_indices = np.where(self.cluster_labels == cluster_id)[0]
            cluster_questions = [self.data[i]['content'] for i in cluster_indices]

            # 计算该聚类的TF-IDF中心
            cluster_center = self.kmeans_model.cluster_centers_[cluster_id]

            # 获取最重要的特征词
            top_features_idx = cluster_center.argsort()[-10:][::-1]
            top_features = [feature_names[i] for i in top_features_idx]
            top_scores = [cluster_center[i] for i in top_features_idx]

            # 选择代表性问题（距离聚类中心最近的问题）
            cluster_tfidf = self.tfidf_matrix[cluster_indices]
            distances = np.linalg.norm(cluster_tfidf.toarray() - cluster_center, axis=1)
            representative_indices = distances.argsort()[:5]
            representative_questions = [cluster_questions[i] for i in representative_indices]

            cluster_analysis[cluster_id] = {
                'size': len(cluster_questions),
                'top_features': list(zip(top_features, top_scores)),
                'representative_questions': representative_questions,
                'all_questions': cluster_questions
            }

            print(f"\n=== 聚类 {cluster_id} ===")
            print(f"问题数量: {len(cluster_questions)}")
            print(f"关键特征词: {', '.join(top_features[:5])}")
            print("代表性问题:")
            for i, q in enumerate(representative_questions, 1):
                print(f"  {i}. {q}")

        return cluster_analysis

    def save_results(self, cluster_analysis):
        """保存聚类结果"""
        print("保存聚类结果...")

        # 为原数据添加聚类标签
        results = []
        for i, item in enumerate(self.data):
            result_item = item.copy()
            result_item['cluster_id'] = int(self.cluster_labels[i])
            result_item['processed_text'] = self.processed_texts[i]
            results.append(result_item)

        # 保存带聚类标签的数据
        with open('fanzhang39/codes/qa/k-means/biology_qa_clustered.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 保存聚类分析结果
        analysis_results = {
            'optimal_k': self.optimal_k,
            'total_questions': len(self.data),
            'cluster_analysis': cluster_analysis,
            'evaluation_metrics': {
                'silhouette_score': float(silhouette_score(self.tfidf_matrix, self.cluster_labels)),
                'calinski_harabasz_score': float(calinski_harabasz_score(self.tfidf_matrix.toarray(), self.cluster_labels))
            }
        }

        with open('fanzhang39/codes/qa/k-means/cluster_analysis_results.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, ensure_ascii=False, indent=2)

        print("结果已保存到:")
        print("- biology_qa_clustered.json (带聚类标签的原数据)")
        print("- cluster_analysis_results.json (聚类分析结果)")

    def generate_report(self, cluster_analysis):
        """生成聚类分析报告"""
        print("\n" + "="*60)
        print("生物类问答聚类分析报告")
        print("="*60)

        print(f"数据概况:")
        print(f"- 总问题数量: {len(self.data)}")
        print(f"- 最优聚类数: {self.optimal_k}")
        print(f"- 特征维度: {self.tfidf_matrix.shape[1]}")

        print(f"\n聚类质量评估:")
        silhouette_avg = silhouette_score(self.tfidf_matrix, self.cluster_labels)
        calinski_score = calinski_harabasz_score(self.tfidf_matrix.toarray(), self.cluster_labels)
        print(f"- 轮廓系数: {silhouette_avg:.4f}")
        print(f"- Calinski-Harabasz指数: {calinski_score:.4f}")

        print(f"\n主题聚类发现:")
        for cluster_id, analysis in cluster_analysis.items():
            print(f"\n聚类 {cluster_id}:")
            top_words = [word for word, _ in analysis['top_features'][:3]]

            print(f"  问题数量: {analysis['size']}")
            print(f"  关键词: {', '.join(top_words)}")

    def run_complete_analysis(self):
        """运行完整的基于词汇相似度的聚类分析流程"""
        print("开始生物类问答词汇相似度聚类分析...")

        # 1. 加载数据
        if not self.load_data():
            return

        # 2. 准备特征（词汇集合和相似度矩阵）
        self.prepare_features()

        # 3. 寻找最优DBSCAN参数
        self.find_optimal_eps()

        # 4. 执行DBSCAN聚类
        self.perform_clustering()

        # 5. 可视化结果
        self.visualize_clusters()

        # 6. 分析聚类特征
        cluster_analysis = self.analyze_clusters()

        # 7. 保存结果
        self.save_results(cluster_analysis)

        # 8. 生成报告
        self.generate_report(cluster_analysis)

        print("\n词汇相似度聚类分析完成!")


def main():
    """主函数"""
    # 数据文件路径
    data_path = "/work1/data/fanzhang39/share/kexue/fanzhang39/codes/qa/k-means/data_biology_only/biology_qa_data.json"

    # 创建分析器并运行分析
    analyzer = BiologyQAClusterAnalyzer(data_path)
    analyzer.run_complete_analysis()


if __name__ == "__main__":
    main()
